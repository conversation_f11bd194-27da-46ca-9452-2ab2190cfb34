import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../models/product.dart';
import '../../models/sale.dart';
import '../../models/category.dart' as model_category;
import '../../providers/settings_provider.dart';
import '../../models/sales_log.dart';
import '../../models/transaction_type.dart';
import '../../providers/product_provider.dart';
import '../../providers/category_provider.dart';
import '../../providers/sales_log_provider.dart';
import '../../utils/currency_utils.dart';
import '../../utils/toast_utils.dart';
import '../../utils/network_status.dart';
import '../../widgets/sale_confirmation_dialog.dart';
import '../../utils/dimens.dart';
import '../../utils/logger_utils.dart';
import '../../providers/set_discount_provider.dart';
import '../../services/set_discount_service.dart';
import 'sale_ui_components.dart';

import '../../utils/device_utils.dart';
import '../../utils/app_colors.dart';

/// **Old 프로젝트의 다중 판매 페이지와 동일한 UI와 기능을 구현한 POS 판매 화면**
class SaleScreen extends ConsumerStatefulWidget {
  const SaleScreen({super.key});

  @override
  ConsumerState<SaleScreen> createState() => _SaleScreenState();
}

class _SaleScreenState extends ConsumerState<SaleScreen>
    with RestorationMixin {
  final Map<int, int> _saleQuantities = {}; // 할인별 적용 수량
  
  // 깜빡임 방지를 위한 ValueNotifier 추가
  final ValueNotifier<Map<int, int>> _productQuantitiesNotifier = ValueNotifier<Map<int, int>>({});
  final ValueNotifier<int> _totalAmountNotifier = ValueNotifier<int>(0);

  @override
  String? get restorationId => 'sale_screen';
  @override
  void restoreState(RestorationBucket? oldBucket, bool initialRestore) {}

  @override
  void initState() {
    super.initState();
    // 초기 데이터 로드
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(productNotifierProvider.notifier).loadProducts();
      ref.read(salesLogNotifierProvider.notifier).loadSalesLogs();
      ref.read(categoryNotifierProvider.notifier).loadCategories();
    });
  }

  @override
  void dispose() {
    _productQuantitiesNotifier.dispose();
    _totalAmountNotifier.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'POS',
          style: Theme.of(context).textTheme.titleLarge!.copyWith(
            fontFamily: 'Pretendard',
            color: Theme.of(context).colorScheme.onPrimary,
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        centerTitle: true,
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Theme.of(context).colorScheme.onPrimary,
        actions: [
          // 새로고침 버튼 추가
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              _refreshPage();
            },
            tooltip: '새로고침',
          ),
        ],
      ),
      body: SafeArea(
        child: Column(
          children: [
            // 상품 및 할인 그리드
            Expanded(
              child: Padding(
                padding: const EdgeInsets.all(4),
                child: RepaintBoundary(
                  child: _ProductGrid(
                    productQuantitiesNotifier: _productQuantitiesNotifier,
                    onProductTap: _onProductTap,
                    onProductLongPress: _onProductLongPress,
                    onProductDecrease: _onProductDecrease,
                    onSaleTap: _onSaleTap,
                    onSaleLongPress: _onSaleLongPress,
                  ),
                ),
              ),
            ),
            
            // 하단 합계 및 판매 버튼
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                gradient: AppColors.backgroundGradient,
                boxShadow: [
                  BoxShadow(
                    color: AppColors.shadow,
                    blurRadius: 12,
                    offset: const Offset(0, -4),
                  ),
                ],
                borderRadius: const BorderRadius.vertical(
                  top: Radius.circular(24),
                ),
              ),
              child: Row(
                children: [
                  // 합계 표시
                  Expanded(
                    child: Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                          colors: [
                            AppColors.surface,
                            AppColors.surfaceVariant,
                          ],
                        ),
                        borderRadius: BorderRadius.circular(16),
                        boxShadow: [
                          BoxShadow(
                            color: AppColors.shadowLight,
                            blurRadius: 8,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            '총 금액',
                            style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                              fontFamily: 'Pretendard',
                              fontSize: 14,
                              color: AppColors.onboardingTextSecondary,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          const SizedBox(height: 4),
                          ValueListenableBuilder<int>(
                            valueListenable: _totalAmountNotifier,
                            builder: (context, totalAmount, child) {
                              return Text(
                                CurrencyUtils.formatCurrency(totalAmount),
                                style: TextStyle(
                                  fontSize: 28,
                                  fontWeight: FontWeight.bold,
                                  color: AppColors.onboardingPrimary,
                                ),
                              );
                            },
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(width: 16),

                  // 판매 버튼
                  ValueListenableBuilder<int>(
                    valueListenable: _totalAmountNotifier,
                    builder: (context, totalAmount, child) {
                      return Container(
                        decoration: BoxDecoration(
                          gradient: totalAmount > 0
                            ? AppColors.primaryGradient
                            : LinearGradient(
                                colors: [
                                  AppColors.secondary,
                                  AppColors.secondaryDark,
                                ],
                              ),
                          borderRadius: BorderRadius.circular(16),
                          boxShadow: totalAmount > 0
                            ? [
                                BoxShadow(
                                  color: AppColors.onboardingPrimary.withValues(alpha: 0.4),
                                  blurRadius: 12,
                                  offset: const Offset(0, 6),
                                ),
                              ]
                            : [],
                        ),
                        child: Material(
                          color: Colors.transparent,
                          borderRadius: BorderRadius.circular(16),
                          child: InkWell(
                            borderRadius: BorderRadius.circular(16),
                            onTap: totalAmount > 0 ? _showSellConfirmDialog : null,
                            child: Container(
                              padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 20),
                              child: Text(
                                '판매',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                  color: totalAmount > 0
                                    ? AppColors.onboardingTextOnPrimary
                                    : AppColors.onboardingTextSecondary,
                                ),
                              ),
                            ),
                          ),
                        ),
                      );
                    },
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }





  // 상품 탭 처리
  void _onProductTap(Product product) {
    LoggerUtils.logDebug('상품 탭됨: ${product.name} (ID: ${product.id})', tag: 'SaleScreen');
    if (product.id == null) return; // id가 null이면 처리하지 않음
    
    final currentQuantities = Map<int, int>.from(_productQuantitiesNotifier.value);
    final currentQuantity = currentQuantities[product.id!] ?? 0;
    LoggerUtils.logDebug('현재 수량: $currentQuantity, 재고: ${product.quantity}', tag: 'SaleScreen');
    
    if (currentQuantity < product.quantity) {
      currentQuantities[product.id!] = currentQuantity + 1;
      _productQuantitiesNotifier.value = currentQuantities;
      LoggerUtils.logDebug('수량 증가: ${currentQuantities[product.id!]}', tag: 'SaleScreen');
      _calculateTotal();
    } else {
      LoggerUtils.logDebug('재고 부족으로 수량 증가 불가', tag: 'SaleScreen');
    }
  }

  // 상품 롱프레스 처리 (나중에 다른 기능으로 사용 예정)
  void _onProductLongPress(Product product) {
    // 나중에 다른 기능을 위해 비워둠
    LoggerUtils.logDebug('상품 롱프레스: ${product.name} (나중에 다른 기능 구현 예정)', tag: 'SaleScreen');
  }

  // 상품 수량 감소 처리
  void _onProductDecrease(Product product) {
    if (product.id == null) return; // id가 null이면 처리하지 않음

    final currentQuantities = Map<int, int>.from(_productQuantitiesNotifier.value);
    final currentQuantity = currentQuantities[product.id!] ?? 0;

    if (currentQuantity > 1) {
      currentQuantities[product.id!] = currentQuantity - 1;
      LoggerUtils.logDebug('수량 감소: ${currentQuantities[product.id!]}', tag: 'SaleScreen');
    } else {
      currentQuantities.remove(product.id!);
      LoggerUtils.logDebug('상품 제거: ${product.name}', tag: 'SaleScreen');
    }

    _productQuantitiesNotifier.value = currentQuantities;
    _calculateTotal();
  }

  // 할인 탭 처리
  void _onSaleTap(Sale sale) {
    setState(() {
      final currentQuantity = _saleQuantities[sale.id] ?? 0;
      _saleQuantities[sale.id!] = currentQuantity + 1;
      _calculateTotal();
    });
  }

  // 할인 롱프레스 처리
  void _onSaleLongPress(Sale sale) {
    setState(() {
      final currentQuantity = _saleQuantities[sale.id] ?? 0;
      if (currentQuantity > 1) {
        _saleQuantities[sale.id!] = currentQuantity - 1;
      } else {
        _saleQuantities.remove(sale.id);
      }
      _calculateTotal();
    });
  }

  // 총 금액 계산 (세트 할인 적용)
  void _calculateTotal() async {
    final allProducts = ref
        .read(productNotifierProvider)
        .products
        .where((product) => product.isActive)
        .toList();

    int total = 0;
    final quantities = _productQuantitiesNotifier.value;

    LoggerUtils.logDebug('총 금액 계산 시작', tag: 'SaleScreen');
    LoggerUtils.logDebug('선택된 상품 수: ${quantities.length}', tag: 'SaleScreen');
    LoggerUtils.logDebug('전체 활성 상품 수: ${allProducts.length}', tag: 'SaleScreen');

    // 상품 금액 합계
    quantities.forEach((productId, quantity) {
      try {
        final product = allProducts.firstWhere((p) => p.id == productId);
        final itemTotal = (product.price * quantity).round();
        total += itemTotal;
        LoggerUtils.logDebug('상품: ${product.name}, 수량: $quantity, 가격: ${product.price}, 소계: $itemTotal', tag: 'SaleScreen');
      } catch (e) {
        LoggerUtils.logDebug('상품을 찾을 수 없음: ID $productId', tag: 'SaleScreen');
        // 상품을 찾을 수 없는 경우 무시
      }
    });

    // 세트 할인 적용
    int finalTotal = total > 0 ? total : 0;
    try {
      final activeSetDiscounts = await ref.read(activeSetDiscountsProvider.future);
      final setDiscountResult = SetDiscountService.calculateOptimalDiscount(
        quantities,
        activeSetDiscounts,
      );

      finalTotal = total - setDiscountResult.totalDiscountAmount;
      if (finalTotal < 0) finalTotal = 0;

      LoggerUtils.logDebug('원래 금액: ${CurrencyUtils.formatCurrency(total)}', tag: 'SaleScreen');
      LoggerUtils.logDebug('세트 할인: ${CurrencyUtils.formatCurrency(setDiscountResult.totalDiscountAmount)}', tag: 'SaleScreen');
      LoggerUtils.logDebug('최종 금액: ${CurrencyUtils.formatCurrency(finalTotal)}', tag: 'SaleScreen');
    } catch (e) {
      LoggerUtils.logError('세트 할인 계산 실패', error: e, tag: 'SaleScreen');
    }

    _totalAmountNotifier.value = finalTotal;
    LoggerUtils.logDebug('총 금액: $finalTotal', tag: 'SaleScreen');
  }

  // 판매 확인 다이얼로그 (세트 할인 정보 포함)
  Future<void> _showSellConfirmDialog() async {
    LoggerUtils.logDebug('판매 확인 다이얼로그 시작', tag: 'SaleScreen');
    final totalAmount = _totalAmountNotifier.value;
    final quantities = _productQuantitiesNotifier.value;
    LoggerUtils.logDebug('총 금액: $totalAmount', tag: 'SaleScreen');
    LoggerUtils.logDebug('선택된 상품 수: ${quantities.length}', tag: 'SaleScreen');

    final allProducts = ref
        .read(productNotifierProvider)
        .products
        .where((product) => product.isActive)
        .toList();

    // 카테고리 정보 가져오기
    final categoriesAsync = ref.read(categoryNotifierProvider);
    final categories = categoriesAsync.hasValue ? categoriesAsync.value! : <model_category.Category>[];

    // SaleItem 리스트 생성
    final saleItems = quantities.entries
        .where((entry) => entry.value > 0)
        .map((entry) {
          try {
            final product = allProducts.firstWhere((p) => p.id == entry.key);
            // 카테고리명과 상품명 조합
            String displayName = product.name;
            try {
              final category = categories.firstWhere(
                (cat) => cat.id == product.categoryId,
              );
              displayName = '[${category.name}] ${product.name}';
            } catch (e) {
              // 카테고리를 찾을 수 없는 경우 원본 상품명 사용
            }

            return SaleItem(
              displayName: displayName,
              quantity: entry.value,
              unitPrice: product.price,
              subtotal: product.price * entry.value,
            );
          } catch (e) {
            return SaleItem(
              displayName: '알 수 없는 상품',
              quantity: entry.value,
              unitPrice: 0,
              subtotal: 0,
            );
          }
        })
        .toList();

    LoggerUtils.logDebug('선택된 상품 수: ${saleItems.length}', tag: 'SaleScreen');

    // 세트 할인 정보 계산
    String? setDiscountInfo;
    try {
      final activeSetDiscounts = await ref.read(activeSetDiscountsProvider.future);
      final setDiscountResult = SetDiscountService.calculateOptimalDiscount(
        quantities,
        activeSetDiscounts,
      );

      if (setDiscountResult.totalDiscountAmount > 0) {
        setDiscountInfo = SetDiscountService.formatDiscountSummary(setDiscountResult);
      }
    } catch (e) {
      LoggerUtils.logError('세트 할인 정보 계산 실패', error: e, tag: 'SaleScreen');
    }

    LoggerUtils.logDebug('다이얼로그 표시 시작', tag: 'SaleScreen');
    final confirmed = await SaleConfirmationDialog.show(
      context: context,
      items: saleItems,
      totalAmount: totalAmount,
      setDiscountInfo: setDiscountInfo,
      onConfirm: () {
        LoggerUtils.logDebug('판매 확인됨 - 판매 처리 시작', tag: 'SaleScreen');
      },
      onCancel: () {
        LoggerUtils.logDebug('판매 취소됨', tag: 'SaleScreen');
      },
    );

    LoggerUtils.logDebug('다이얼로그 결과: $confirmed', tag: 'SaleScreen');
    if (confirmed == true) {
      _completeSale();
    }
  }

  // 판매 완료 처리 (세트 할인 포함)
  Future<void> _completeSale() async {
    try {
      LoggerUtils.logDebug('판매 처리 시작', tag: 'SaleScreen');
      LoggerUtils.logDebug('네트워크 상태: ${NetworkStatusUtil.isOnline}', tag: 'SaleScreen');
      final quantities = _productQuantitiesNotifier.value;
      LoggerUtils.logDebug('선택된 상품 수: ${quantities.length}', tag: 'SaleScreen');

      // 네트워크 상태 확인
      LoggerUtils.logDebug('현재 네트워크 상태: ${NetworkStatusUtil.isOnline}', tag: 'SaleScreen');

      // 실제 판매할 총 수량 계산
      final totalQuantity = quantities.values.fold<int>(0, (sum, quantity) => sum + quantity);
      // 여러 종류의 상품을 선택했을 때만 묶음 판매로 처리 (수량이 아닌 상품 종류 개수 기준)
      // 고유성 보장을 위해 microsecondsSinceEpoch 사용
      final batchSaleId = quantities.length > 1 ? 'batch_${DateTime.now().microsecondsSinceEpoch}' : null;

      final soldOrAppliedItemDescriptions = <String>[];

      final allProducts = ref
          .read(productNotifierProvider)
          .products
          .where((product) => product.isActive)
          .toList();

      LoggerUtils.logDebug('활성 상품 수: ${allProducts.length}', tag: 'SaleScreen');

      // 세트 할인 계산
      SetDiscountResult setDiscountResult = SetDiscountResult.empty();
      try {
        final activeSetDiscounts = await ref.read(activeSetDiscountsProvider.future);
        setDiscountResult = SetDiscountService.calculateOptimalDiscount(
          quantities,
          activeSetDiscounts,
        );
        LoggerUtils.logDebug('세트 할인 계산 완료: ${setDiscountResult.totalDiscountAmount}원', tag: 'SaleScreen');
      } catch (e) {
        LoggerUtils.logError('세트 할인 계산 실패', error: e, tag: 'SaleScreen');
      }

      // 상품 판매 처리 (원본과 동일)
      for (final entry in quantities.entries) {
        if (entry.value > 0) {
          LoggerUtils.logDebug('처리 중인 상품 ID: ${entry.key}, 수량: ${entry.value}', tag: 'SaleScreen');
          
          final product = allProducts.firstWhere((p) => p.id == entry.key);
          LoggerUtils.logDebug('찾은 상품: ${product.name}, 현재 재고: ${product.quantity}', tag: 'SaleScreen');

          if (product.quantity >= entry.value) {
            // 재고 차감 (원본과 동일)
            LoggerUtils.logDebug('재고 차감 전: ${product.quantity}', tag: 'SaleScreen');
            final updatedProduct = product.copyWith(
              quantity: product.quantity - entry.value,
            );
            LoggerUtils.logDebug('재고 차감 후: ${updatedProduct.quantity}', tag: 'SaleScreen');
            
            LoggerUtils.logDebug('상품 업데이트 시작', tag: 'SaleScreen');
            await ref
                .read(productNotifierProvider.notifier)
                .updateProduct(updatedProduct);
            LoggerUtils.logDebug('상품 업데이트 완료', tag: 'SaleScreen');

            final itemTotal = product.price * entry.value;
            soldOrAppliedItemDescriptions.add(
              "${product.name} ${entry.value}개 (판매)",
            );

            // 세트 할인 정보 계산 (이 상품에 적용된 할인)
            int productSetDiscountAmount = 0;
            String? productSetDiscountNames;

            if (setDiscountResult.totalDiscountAmount > 0 &&
                setDiscountResult.usedProductQuantities.containsKey(product.id)) {
              // 이 상품이 세트 할인에 포함된 경우
              final appliedDiscountNames = setDiscountResult.appliedDiscounts
                  .where((applied) => applied.setDiscount.productIds.contains(product.id))
                  .map((applied) => applied.setDiscount.name)
                  .toList();

              if (appliedDiscountNames.isNotEmpty) {
                // 비례 배분으로 할인 금액 계산
                final totalOriginalAmount = quantities.entries
                    .map((e) => allProducts.firstWhere((p) => p.id == e.key).price * e.value)
                    .fold<int>(0, (sum, amount) => sum + amount);

                if (totalOriginalAmount > 0) {
                  productSetDiscountAmount =
                      (setDiscountResult.totalDiscountAmount * itemTotal / totalOriginalAmount).round();
                }

                productSetDiscountNames = appliedDiscountNames.join(', ');
              }
            }

            // 할인이 적용된 실제 판매 금액 계산
            final finalItemTotal = itemTotal - productSetDiscountAmount;

            // 판매 로그 생성 (할인이 적용된 금액으로 저장)
            final salesLog = SalesLog.create(
              id: 0, // DB에서 자동 생성
              productId: product.id,
              productName: product.name,
              soldPrice: product.price,
              soldQuantity: entry.value,
              totalAmount: finalItemTotal, // 할인이 적용된 최종 금액
              sellerName: product.sellerName,
              transactionType: TransactionType.sale,
              batchSaleId: batchSaleId,
              setDiscountAmount: productSetDiscountAmount,
              setDiscountNames: productSetDiscountNames,
            );

            // 판매 로그 추가 (강화된 에러 처리)
            LoggerUtils.logDebug('판매 로그 추가 시작', tag: 'SaleScreen');
            LoggerUtils.logDebug('batchSaleId: $batchSaleId, totalQuantity: $totalQuantity', tag: 'SaleScreen');
            LoggerUtils.logDebug('판매 로그 데이터: ${salesLog.toJson()}', tag: 'SaleScreen');
            
            try {
              await ref
                  .read(salesLogNotifierProvider.notifier)
                  .addSalesLog(salesLog);
              LoggerUtils.logDebug('판매 로그 추가 완료', tag: 'SaleScreen');
            } catch (e) {
              LoggerUtils.logError('판매 로그 추가 실패 - 상품: ${product.name}, 수량: ${entry.value}', tag: 'SaleScreen', error: e);
              // 에러가 발생해도 다른 상품 처리는 계속 진행
              LoggerUtils.logDebug('다른 상품 처리 계속 진행', tag: 'SaleScreen');
            }
                
            // 디버깅 로그
            LoggerUtils.logDebug('판매 완료: ${product.name} x${entry.value}개, 총액: ${CurrencyUtils.formatCurrency(itemTotal)}', tag: 'SaleScreen');
          } else {
            // 재고 부족 처리 (원본과 동일)
            if (mounted) {
              // 원본과 동일한 Toast 메시지 (재고 부족)
              ToastUtils.showToast(
                context,
                '${product.name} 재고 부족 (현재 ${product.quantity}개)', // 원본과 동일한 메시지
              );
            }
          }
        }
      }

      LoggerUtils.logDebug('판매 처리 완료', tag: 'SaleScreen');
      
      // 선택 초기화
      if (mounted) {
        LoggerUtils.logDebug('상태 초기화 시작', tag: 'SaleScreen');
        _productQuantitiesNotifier.value = {};
        _totalAmountNotifier.value = 0;
        LoggerUtils.logDebug('상태 초기화 완료', tag: 'SaleScreen');
      }

      // 판매 완료 후 상태 갱신
      LoggerUtils.logDebug('상태 갱신 시작', tag: 'SaleScreen');
      
      // ProductNotifier는 이미 updateProduct에서 자동으로 갱신됨
      // 페이징 컨트롤러만 갱신
      ref.read(productNotifierProvider.notifier).loadProducts(showLoading: false);
      
      LoggerUtils.logDebug('상품 목록 갱신 완료', tag: 'SaleScreen');
      
      LoggerUtils.logDebug('상태 갱신 완료', tag: 'SaleScreen');

      // 성공적으로 판매 완료 후 토스트 메시지 표시
      if (mounted) {
        ToastUtils.showToast(
          context,
          '판매가 성공적으로 처리되었습니다.',
        );
      }
    } catch (e) {
      if (mounted) {
        ToastUtils.showError(context, '판매 처리 중 오류 발생: $e');
      }
    } finally {
      LoggerUtils.logDebug('판매 처리 최종 완료', tag: 'SaleScreen');
    }
  }

  // 페이지 새로고침
  void _refreshPage() {
    // 선택 초기화
    _productQuantitiesNotifier.value = {};
    _totalAmountNotifier.value = 0;

    // 실시간 동기화로 자동 갱신되므로 수동 새로고침 불필요
    LoggerUtils.logInfo('페이지 새로고침 - 실시간 동기화로 자동 갱신됨', tag: 'SaleScreen');
  }
}

/// 상품 그리드 위젯 - 깜빡임 방지를 위한 별도 위젯
class _ProductGrid extends ConsumerWidget {
  final ValueNotifier<Map<int, int>> productQuantitiesNotifier;
  final Function(Product) onProductTap;
  final Function(Product) onProductLongPress;
  final Function(Product) onProductDecrease;
  final Function(Sale) onSaleTap;
  final Function(Sale) onSaleLongPress;

  const _ProductGrid({
    required this.productQuantitiesNotifier,
    required this.onProductTap,
    required this.onProductLongPress,
    required this.onProductDecrease,
    required this.onSaleTap,
    required this.onSaleLongPress,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // 상품 데이터를 watch로 가져와서 실시간 업데이트
    final productState = ref.watch(productNotifierProvider);
    
    // 실시간으로 필터링된 상품 목록 사용 (정렬 적용된 filteredProducts 사용)
    // 품절 상품도 표시하여 정렬 효과를 확인할 수 있도록 함 (isActive 필터 제거)
    List<Product> allProducts = productState.filteredProducts;

    final orientation = MediaQuery.of(context).orientation;
    final isPortrait = orientation == Orientation.portrait;
    
    // 화면 방향에 따라 설정 Provider의 값을 직접 사용 (watch 대신 read 사용)
    final columns = isPortrait
        ? ref.watch(saleColumnsPortraitProvider)
        : ref.watch(saleColumnsLandscapeProvider);
    
    // 디버깅 정보 출력
    final deviceInfo = Dimens.getDeviceInfo(context);
    LoggerUtils.logDebug('Sale Screen - Device Info: $deviceInfo', tag: 'SaleScreen');
    LoggerUtils.logDebug('Sale Screen - Orientation: ${isPortrait ? "Portrait" : "Landscape"}', tag: 'SaleScreen');
    LoggerUtils.logDebug('Sale Screen - Columns: $columns', tag: 'SaleScreen');
    
    return _buildCategoryGroupedProducts(allProducts, columns, ref);
  }

  /// 카테고리별로 그룹화된 상품 목록을 빌드
  Widget _buildCategoryGroupedProducts(List<Product> products, int columns, WidgetRef ref) {
    // 카테고리별로 상품 그룹화
    final Map<int, List<Product>> groupedProducts = {};
    for (final product in products) {
      final categoryId = product.categoryId;
      if (!groupedProducts.containsKey(categoryId)) {
        groupedProducts[categoryId] = [];
      }
      groupedProducts[categoryId]!.add(product);
    }

    // 디버깅: 상품별 카테고리 ID 로깅
    for (final product in products) {
      LoggerUtils.logDebug('상품 "${product.name}" - categoryId: ${product.categoryId}', tag: 'SaleScreen');
    }
    LoggerUtils.logDebug('그룹화된 카테고리: ${groupedProducts.keys.toList()}', tag: 'SaleScreen');

    // 카테고리 데이터 가져오기
    final categoriesAsyncValue = ref.watch(categoryNotifierProvider);
    
    return categoriesAsyncValue.when(
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stackTrace) => Center(child: Text('카테고리 로딩 오류: $error')),
      data: (categories) {
        // 모든 카테고리를 정렬 순서에 따라 표시 (상품이 없어도 표시)
        final sortedCategories = [...categories]
          ..sort((a, b) => a.sortOrder.compareTo(b.sortOrder));
        
        LoggerUtils.logDebug('전체 카테고리 수: ${categories.length}', tag: 'SaleScreen');
        for (final category in categories) {
          final productCount = groupedProducts[category.id]?.length ?? 0;
          LoggerUtils.logDebug('카테고리 "${category.name}" (ID: ${category.id}) - 상품 수: $productCount', tag: 'SaleScreen');
        }
        
        return AnimatedSwitcher(
          duration: const Duration(milliseconds: 400),
          child: Align(
            alignment: Alignment.topCenter,
            child: SingleChildScrollView( // 오버플로우 방지를 위한 스크롤 래퍼
              key: ValueKey('sale_categories_${sortedCategories.length}'),
              padding: const EdgeInsets.all(8.0),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: sortedCategories.map((category) {
                  final categoryProducts = groupedProducts[category.id] ?? []; // 빈 리스트도 허용

                  return AnimatedContainer(
                    key: ValueKey('sale_category_${category.id}'),
                    duration: const Duration(milliseconds: 250),
                    curve: Curves.easeInOut,
                    child: _SaleCategorySection(
                      category: category,
                      products: categoryProducts,
                      columns: columns,
                      productQuantitiesNotifier: productQuantitiesNotifier,
                      onProductTap: onProductTap,
                      onProductLongPress: onProductLongPress,
                      onProductDecrease: onProductDecrease,
                    ),
                  );
                }).toList(),
              ),
            ),
          ),
        );
      },
    );
  }
}

/// 개별 상품 그리드 아이템 - 깜빡임 방지를 위한 별도 위젯
class _ProductGridItem extends StatefulWidget {
  final Product product;
  final ValueNotifier<Map<int, int>> productQuantitiesNotifier;
  final Function(Product) onTap;
  final Function(Product) onLongPress;
  final Function(Product) onDecrease; // 수량 감소 콜백 추가
  final int columns;

  const _ProductGridItem({
    super.key,
    required this.product,
    required this.productQuantitiesNotifier,
    required this.onTap,
    required this.onLongPress,
    required this.onDecrease,
    required this.columns,
  });

  @override
  State<_ProductGridItem> createState() => _ProductGridItemState();
}

class _ProductGridItemState extends State<_ProductGridItem> {
  int _quantity = 0;

  @override
  void initState() {
    super.initState();
    // 초기 수량 설정
    _quantity = widget.productQuantitiesNotifier.value[widget.product.id] ?? 0;
    
    // 수량 변경 리스너 등록
    widget.productQuantitiesNotifier.addListener(_onQuantitiesChanged);
  }

  @override
  void dispose() {
    widget.productQuantitiesNotifier.removeListener(_onQuantitiesChanged);
    super.dispose();
  }

  void _onQuantitiesChanged() {
    final newQuantity = widget.productQuantitiesNotifier.value[widget.product.id] ?? 0;
    if (_quantity != newQuantity) {
      setState(() {
        _quantity = newQuantity;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final isOutOfStock = widget.product.quantity <= 0;
    final isSelected = _quantity > 0;
    
    return SaleUiComponents.buildProductGridItem(
      product: widget.product,
      isSelected: isSelected,
      quantity: _quantity,
      onTap: isOutOfStock ? null : () => widget.onTap(widget.product),
      onLongPress: isSelected ? () => widget.onLongPress(widget.product) : null,
      onDecrease: isSelected ? () => widget.onDecrease(widget.product) : null,
      columns: widget.columns,
    );
  }
}

/// 판매 화면용 카테고리별 상품 섹션 위젯
class _SaleCategorySection extends StatelessWidget {
  final model_category.Category category;
  final List<Product> products;
  final int columns;
  final ValueNotifier<Map<int, int>> productQuantitiesNotifier;
  final Function(Product) onProductTap;
  final Function(Product) onProductLongPress;
  final Function(Product) onProductDecrease;

  const _SaleCategorySection({
    required this.category,
    required this.products,
    required this.columns,
    required this.productQuantitiesNotifier,
    required this.onProductTap,
    required this.onProductLongPress,
    required this.onProductDecrease,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 카테고리 제목 - Material Design 3 Chips 스타일 (상품과 정렬 맞춤)
        Container(
          margin: EdgeInsets.only(
            left: DeviceUtils.getOptimalCardSpacing(context), // 상품 카드와 정렬 맞춤
            right: 16.0, 
            bottom: 8.0
          ), // 아래 간격 줄임
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 14.0, vertical: 6.0),
                decoration: BoxDecoration(
                  color: Color(category.color).withValues(alpha: 0.15),
                  borderRadius: BorderRadius.circular(
                    DeviceUtils.isSmartphone(context) ? 12.0 : 16.0, // 상품 카드와 동일한 라운드
                  ),
                  border: Border.all(
                    color: Color(category.color).withValues(alpha: 0.8),
                    width: 1.5,
                  ),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // 카테고리 아이콘
                    Icon(
                      Icons.folder_outlined,
                      size: 18.0,
                      color: Color(category.color),
                    ),
                    const SizedBox(width: 8.0),
                    Text(
                      category.name,
                      style: TextStyle(
                        fontSize: 14.0,
                        fontWeight: FontWeight.bold,
                        color: Color(category.color),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
        
        // 카테고리 내 상품들 - 빈 카테고리도 처리
        if (products.isEmpty)
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 16.0),
            child: Text(
              '이 카테고리에는 등록된 상품이 없습니다.',
              style: TextStyle(
                fontSize: 16.0,
                color: Colors.grey.shade600,
                fontStyle: FontStyle.italic,
              ),
              textAlign: TextAlign.left,
            ),
          )
        else
          AnimatedSwitcher(
            duration: const Duration(milliseconds: 300),
            child: Align(
              alignment: Alignment.topCenter,
              child: GridView.builder(
                key: ValueKey('sale_grid_${products.length}'),
                shrinkWrap: true, // Column 내부에서 사용하므로 필수
                physics: const NeverScrollableScrollPhysics(), // 상위 스크롤과 충돌 방지
                padding: const EdgeInsets.only(bottom: 24.0), // 카테고리 간 간격 늘림
                gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: columns,
                  crossAxisSpacing: DeviceUtils.getOptimalCardSpacing(context),
                  mainAxisSpacing: DeviceUtils.getOptimalCardSpacing(context),
                  childAspectRatio: DeviceUtils.getOptimalCardAspectRatio(context, columns),
                ),
            itemCount: products.length,
              itemBuilder: (context, index) {
                final product = products[index];
                return AnimatedContainer(
                  key: ValueKey('sale_product_${product.id}'),
                  duration: const Duration(milliseconds: 200),
                  curve: Curves.easeInOut,
                  child: _ProductGridItem(
                    key: ValueKey(product.id),
                    product: product,
                    productQuantitiesNotifier: productQuantitiesNotifier,
                    onTap: onProductTap,
                    onLongPress: onProductLongPress,
                    onDecrease: onProductDecrease,
                    columns: columns,
                  ),
                );
              },
            ),
          ),
        ),
      ],
    );
  }
}