import 'package:flutter/material.dart';
import '../../models/product.dart';
import '../../models/sale.dart';
import '../../utils/currency_utils.dart';
import '../../widgets/product_image.dart';
import '../../utils/responsive_text_utils.dart';
import '../../utils/device_utils.dart';
import '../../utils/app_colors.dart';

/// 판매 화면의 UI 컴포넌트들을 담당하는 클래스
///
/// 주요 기능:
/// - 상품 그리드 아이템
/// - 할인 그리드 아이템
/// - 반응형 디자인 유틸리티
/// - 현대적 Material Design 3 스타일 적용
class SaleUiComponents {
  /// 반응형 열 수 계산 (기본값만 제공, 실제로는 설정값 사용 권장)
  /// 이 메서드는 설정값이 없을 때의 기본값만 제공합니다.
  static int getCrossAxisCount(BuildContext context) {
    return DeviceUtils.getOptimalGridColumns(context);
  }

  /// 반응형 카드 마진
  static double getCardMargin(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth >= 600) return 2.0; // 태블릿
    return 1.0; // 모바일
  }

  /// 반응형 카드 elevation
  static double getCardElevation(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth >= 600) return 4.0; // 태블릿
    return 2.0; // 모바일
  }

  /// 반응형 카드 corner radius
  static double getCardCornerRadius(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth >= 600) return 8.0; // 태블릿
    return 6.0; // 모바일
  }

  /// 반응형 텍스트 마진
  static EdgeInsets getTextMargin(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth >= 600) {
      return const EdgeInsets.symmetric(horizontal: 8, vertical: 4); // 태블릿
    }
    return const EdgeInsets.symmetric(horizontal: 6, vertical: 3); // 모바일
  }

  /// 반응형 수량 텍스트 패딩
  static EdgeInsets getQuantityTextPadding(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth >= 600) {
      return const EdgeInsets.symmetric(horizontal: 12, vertical: 6); // 태블릿
    }
    return const EdgeInsets.symmetric(horizontal: 8, vertical: 4); // 모바일
  }

  /// 반응형 상품명 텍스트 스타일
  static TextStyle getProductNameTextStyle(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth >= 600) {
      return Theme.of(context).textTheme.titleMedium!.copyWith(
        fontFamily: 'Pretendard',
        fontSize: 14, 
        fontWeight: FontWeight.bold,
      ); // 태블릿
    }
    return Theme.of(context).textTheme.titleSmall!.copyWith(
      fontFamily: 'Pretendard',
      fontSize: 12, 
      fontWeight: FontWeight.bold,
    ); // 모바일
  }

  /// 반응형 가격 텍스트 스타일
  static TextStyle getPriceTextStyle(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth >= 600) {
      return Theme.of(context).textTheme.bodyMedium!.copyWith(
        fontFamily: 'Pretendard',
        fontSize: 12, 
        color: AppColors.onboardingPrimary,
      ); // 태블릿
    }
    return Theme.of(context).textTheme.bodySmall!.copyWith(
      fontFamily: 'Pretendard',
      fontSize: 10, 
      color: AppColors.onboardingPrimary,
    ); // 모바일
  }

  /// 반응형 수량 텍스트 스타일
  static TextStyle getQuantityTextStyle(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth >= 600) {
      return Theme.of(context).textTheme.titleSmall!.copyWith(
        fontFamily: 'Pretendard',
        fontSize: 16,
        fontWeight: FontWeight.bold,
        color: Colors.white,
      ); // 태블릿
    }
    return Theme.of(context).textTheme.titleSmall!.copyWith(
      fontFamily: 'Pretendard',
      fontSize: 14,
      fontWeight: FontWeight.bold,
      color: Colors.white,
    ); // 모바일
  }

  /// 상품 그리드 아이템 위젯
  static Widget buildProductGridItem({
    required Product product,
    required bool isSelected,
    required int quantity,
    required VoidCallback? onTap,
    required VoidCallback? onLongPress,
    required VoidCallback? onDecrease, // - 버튼 콜백 추가
    required int columns,
  }) {
    return _ProductGridItem(
      product: product,
      isSelected: isSelected,
      quantity: quantity,
      onTap: onTap,
      onLongPress: onLongPress,
      onDecrease: onDecrease,
      columns: columns,
    );
  }

  /// 할인 그리드 아이템 위젯
  static Widget buildSaleGridItem({
    required Sale sale,
    required bool isSelected,
    required int quantity,
    required VoidCallback? onTap,
    required VoidCallback? onLongPress,
  }) {
    return _SaleGridItem(
      sale: sale,
      isSelected: isSelected,
      quantity: quantity,
      onTap: onTap,
      onLongPress: onLongPress,
    );
  }

  /// 총 합계 표시 위젯
  static Widget buildTotalAmountWidget({
    required BuildContext context,
    required int totalAmount,
    required VoidCallback? onSellPressed,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // 총 합계 라벨과 금액
          Row(
            children: [
              Text('총 합계:', style: Theme.of(context).textTheme.titleMedium!.copyWith(fontFamily: 'Pretendard', fontSize: 18)),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  CurrencyUtils.formatCurrency(totalAmount),
                  textAlign: TextAlign.end,
                  style: Theme.of(context).textTheme.titleMedium!.copyWith(
                    fontFamily: 'Pretendard',
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          // 판매 버튼
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: totalAmount > 0 ? onSellPressed : null,
              style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
              child: const Text('선택 상품 판매'),
            ),
          ),
        ],
      ),
    );
  }
}

/// 상품 그리드 아이템 위젯
class _ProductGridItem extends StatelessWidget {
  final Product product;
  final bool isSelected;
  final int quantity;
  final VoidCallback? onTap;
  final VoidCallback? onLongPress;
  final VoidCallback? onDecrease; // - 버튼 콜백 추가
  final int columns; // 열 수 추가

  const _ProductGridItem({
    required this.product,
    required this.isSelected,
    required this.quantity,
    this.onTap,
    this.onLongPress,
    this.onDecrease,
    required this.columns,
  });

  @override
  Widget build(BuildContext context) {
    final isSmartphone = DeviceUtils.isSmartphone(context);
    final isTablet = !isSmartphone;
    final borderRadius = isTablet ? 16.0 : 12.0;

    return Container(
      margin: DeviceUtils.getCardMargin(context, columns),
      decoration: BoxDecoration(
        gradient: product.isOutOfStock
          ? LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                AppColors.secondary.withValues(alpha: 0.3),
                AppColors.secondaryDark.withValues(alpha: 0.3),
              ],
            )
          : isSelected
            ? LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  AppColors.onboardingPrimary.withValues(alpha: 0.2),
                  AppColors.onboardingPrimaryLight.withValues(alpha: 0.2),
                ],
              )
            : AppColors.cardGradient,
        borderRadius: BorderRadius.circular(borderRadius),
      ),
      child: Material(
        color: Colors.transparent,
        borderRadius: BorderRadius.circular(borderRadius),
        child: InkWell(
          borderRadius: BorderRadius.circular(borderRadius),
          onTap: onTap,
          onLongPress: onLongPress,
          child: Padding(
            padding: EdgeInsets.only(
              left: DeviceUtils.getCardPadding(context, columns).left,
              right: DeviceUtils.getCardPadding(context, columns).right,
              top: DeviceUtils.getCardPadding(context, columns).top,
              bottom: 0.2, // 하단 패딩 더욱 최소화 - 텍스트 아래 공간 축소
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              mainAxisSize: MainAxisSize.max, // 카드 전체 높이 사용
              mainAxisAlignment: MainAxisAlignment.start, // 위에서부터 배치
              children: [
                // 이미지 영역 - 가로 공간 100% 활용하는 정사각형
                AspectRatio(
                  aspectRatio: 1.0, // 완벽한 정사각형
                  child: Stack(
                    children: [
                      Container(
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                            colors: [
                              AppColors.surfaceVariant,
                              AppColors.secondary.withValues(alpha: 0.5),
                            ],
                          ),
                          borderRadius: BorderRadius.circular(borderRadius * 0.7),
                        ),
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(borderRadius * 0.7),
                          child: product.imagePath?.isNotEmpty == true
                              ? ProductImage(
                                  imagePath: product.imagePath!,
                                  fit: BoxFit.cover, // 크롭된 이미지 표시
                                )
                              : Container(
                                  decoration: BoxDecoration(
                                    gradient: LinearGradient(
                                      begin: Alignment.topLeft,
                                      end: Alignment.bottomRight,
                                      colors: [
                                        AppColors.secondary,
                                        AppColors.secondaryLight,
                                      ],
                                    ),
                                  ),
                                  child: Icon(
                                    Icons.image_outlined,
                                    color: AppColors.onboardingTextSecondary,
                                    size: isTablet ? 48.0 : 32.0,
                                  ),
                                ),
                        ),
                      ),

                      // 품절 오버레이
                      if (product.isOutOfStock)
                        Positioned.fill(
                          child: Container(
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                begin: Alignment.topLeft,
                                end: Alignment.bottomRight,
                                colors: [
                                  AppColors.error.withValues(alpha: 0.8),
                                  AppColors.errorLight.withValues(alpha: 0.6),
                                ],
                              ),
                              borderRadius: BorderRadius.circular(borderRadius * 0.7),
                            ),
                            child: Center(
                              child: Container(
                                padding: EdgeInsets.symmetric(
                                  horizontal: isTablet ? 16.0 : 12.0,
                                  vertical: isTablet ? 8.0 : 6.0,
                                ),
                                decoration: BoxDecoration(
                                  color: AppColors.onboardingDarkBrown.withValues(alpha: 0.9),
                                  borderRadius: BorderRadius.circular(borderRadius * 0.5),
                                ),
                                child: Text(
                                  '품절',
                                  style: Theme.of(context).textTheme.titleMedium!.copyWith(
                                    fontFamily: 'Pretendard',
                                    color: AppColors.onboardingTextOnDark,
                                    fontSize: isTablet ? 20.0 : 16.0,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ),

                      // 선택 오버레이 및 수량 표시
                      if (isSelected)
                        Positioned.fill(
                          child: Container(
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                begin: Alignment.topLeft,
                                end: Alignment.bottomRight,
                                colors: [
                                  AppColors.onboardingPrimary.withValues(alpha: 0.7),
                                  AppColors.onboardingPrimaryLight.withValues(alpha: 0.5),
                                ],
                              ),
                              borderRadius: BorderRadius.circular(borderRadius * 0.7),
                            ),
                            child: Center(
                              child: Container(
                                padding: EdgeInsets.symmetric(
                                  horizontal: isTablet ? 20.0 : 16.0,
                                  vertical: isTablet ? 12.0 : 8.0,
                                ),
                                decoration: BoxDecoration(
                                  gradient: AppColors.primaryGradient,
                                  borderRadius: BorderRadius.circular(borderRadius * 0.5),
                                  boxShadow: [
                                    BoxShadow(
                                      color: AppColors.onboardingPrimary.withValues(alpha: 0.4),
                                      blurRadius: 8,
                                      offset: const Offset(0, 4),
                                    ),
                                  ],
                                ),
                                child: Text(
                                  quantity.toString(),
                                  style: TextStyle(
                                    color: AppColors.onboardingTextOnPrimary,
                                    fontSize: isTablet ? 24.0 : 20.0,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ),

                      // 재고 수량 배지 (우측 하단) - 항상 표시
                      if (product.quantity > 0)
                        Positioned(
                          right: DeviceUtils.getQuantityBadgePosition(context, columns).right,
                          bottom: DeviceUtils.getQuantityBadgePosition(context, columns).bottom,
                          child: Container(
                            width: DeviceUtils.getQuantityBadgeSize(context, columns),
                            height: DeviceUtils.getQuantityBadgeSize(context, columns),
                            decoration: BoxDecoration(
                              gradient: product.isOutOfStock
                                ? LinearGradient(
                                    colors: [
                                      AppColors.error.withValues(alpha: 0.8),
                                      AppColors.errorLight.withValues(alpha: 0.6),
                                    ],
                                  )
                                : LinearGradient(
                                    colors: [
                                      AppColors.onboardingPrimary.withValues(alpha: 0.9), // 주황색 계열
                                      AppColors.onboardingPrimaryDark.withValues(alpha: 0.9), // 진한 주황색
                                    ],
                                  ),
                              borderRadius: BorderRadius.circular(
                                DeviceUtils.getQuantityBadgeBorderRadius(context, columns), // 라운드 사각형
                              ),
                              border: Border.all(
                                color: AppColors.surface,
                                width: 1.5, // 2.0에서 1.5로 테두리 굵기 감소
                              ),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.black.withValues(alpha: 0.3),
                                  blurRadius: 4,
                                  offset: const Offset(0, 2),
                                ),
                              ],
                            ),
                            child: Center(
                              child: Text(
                                product.quantity > 99 ? '99+' : '${product.quantity}',
                                style: TextStyle(
                                  color: AppColors.onboardingTextOnPrimary,
                                  fontSize: DeviceUtils.getQuantityBadgeTextSize(context, columns),
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          ),
                        ),

                      // 수량 감소 버튼 (좌측 하단) - 선택된 상품에만 표시
                      if (isSelected && quantity > 0 && onDecrease != null)
                        Positioned(
                          left: DeviceUtils.getQuantityBadgePosition(context, columns).right,
                          bottom: DeviceUtils.getQuantityBadgePosition(context, columns).bottom,
                          child: GestureDetector(
                            onTap: onDecrease,
                            child: Container(
                              width: DeviceUtils.getQuantityBadgeSize(context, columns),
                              height: DeviceUtils.getQuantityBadgeSize(context, columns),
                              decoration: BoxDecoration(
                                gradient: LinearGradient(
                                  colors: [
                                    AppColors.error.withValues(alpha: 0.9),
                                    AppColors.error.withValues(alpha: 0.7),
                                  ],
                                ),
                                borderRadius: BorderRadius.circular(
                                  DeviceUtils.getQuantityBadgeBorderRadius(context, columns),
                                ),
                                border: Border.all(
                                  color: AppColors.surface,
                                  width: 1.5,
                                ),
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.black.withValues(alpha: 0.3),
                                    blurRadius: 4,
                                    offset: const Offset(0, 2),
                                  ),
                                ],
                              ),
                              child: Center(
                                child: Icon(
                                  Icons.remove,
                                  color: AppColors.onboardingTextOnPrimary,
                                  size: DeviceUtils.getQuantityBadgeTextSize(context, columns) * 1.2,
                                ),
                              ),
                            ),
                          ),
                        ),
                    ],
                  ),
                ),

                // 가격 - 이미지 바로 아래로 이동
                Container(
                  width: double.infinity,
                  margin: EdgeInsets.only(
                    left: ResponsiveTextUtils.getTextPadding(columns).left,
                    right: ResponsiveTextUtils.getTextPadding(columns).right,
                    top: DeviceUtils.getImageTextSpacing(context, columns), // 열 수에 따라 동적 조정
                  ),
                  padding: DeviceUtils.getPriceQuantityPadding(context, columns),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        const Color(0xFFA0D8A0).withValues(alpha: 0.1), // 연한 초록색
                        const Color(0xFF8FCC8F).withValues(alpha: 0.1), // 조금 더 진한 연한 초록색
                      ],
                    ),
                    borderRadius: BorderRadius.circular(borderRadius * 0.3),
                  ),
                  child: Text(
                    CurrencyUtils.formatCurrency(product.price) + '원',
                    style: TextStyle(
                      fontSize: DeviceUtils.getUnifiedTextSize(context, columns),
                      color: const Color(0xFF4CAF50), // 초록색으로 변경
                      fontWeight: FontWeight.w700,
                    ),
                    textAlign: TextAlign.center,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),

                SizedBox(height: DeviceUtils.getComponentSpacing(context, columns)), // 추가 간격 제거하여 더욱 컴팩트하게

                // 상품명 - 가격 아래로 이동 (2줄까지 최적화된 높이) - Expanded로 감싸서 남은 공간 활용
                Expanded(
                  child: Container(
                    margin: EdgeInsets.only(
                      left: ResponsiveTextUtils.getTextPadding(columns).left,
                      right: ResponsiveTextUtils.getTextPadding(columns).right,
                      // bottom margin 제거하여 하단 여백 축소
                    ),
                    child: Align(
                      alignment: Alignment(0.0, -0.7), // 상단에서 살짝 아래로 텍스트 배치
                      child: Text(
                        product.name,
                        maxLines: 2, // 2줄 고정
                        overflow: TextOverflow.ellipsis,
                        textAlign: TextAlign.center, // 중앙 정렬
                        style: TextStyle(
                          fontSize: DeviceUtils.getUnifiedTextSize(context, columns), // 통합 텍스트 크기 사용
                          color: product.isOutOfStock
                            ? AppColors.onboardingTextSecondary
                            : isSelected
                              ? AppColors.onboardingPrimary
                              : AppColors.onboardingTextPrimary,
                          fontWeight: FontWeight.w600,
                          height: 1.2, // 적절한 줄간격으로 되돌림 (가독성 확보)
                          fontFamily: 'Pretendard',
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

/// 할인 그리드 아이템 위젯
class _SaleGridItem extends StatelessWidget {
  final Sale sale;
  final bool isSelected;
  final int quantity;
  final VoidCallback? onTap;
  final VoidCallback? onLongPress;

  const _SaleGridItem({
    required this.sale,
    required this.isSelected,
    required this.quantity,
    this.onTap,
    this.onLongPress,
  });

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isTablet = screenWidth > 600;
    final borderRadius = isTablet ? 16.0 : 12.0;

    return Container(
      margin: EdgeInsets.all(isTablet ? 6.0 : 4.0),
      decoration: BoxDecoration(
        gradient: isSelected
          ? LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                AppColors.onboardingAccent.withValues(alpha: 0.3),
                AppColors.onboardingAccentLight.withValues(alpha: 0.2),
              ],
            )
          : LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                AppColors.onboardingAccent.withValues(alpha: 0.1),
                AppColors.onboardingAccentLight.withValues(alpha: 0.05),
              ],
            ),
        borderRadius: BorderRadius.circular(borderRadius),
      ),
      child: Material(
        color: Colors.transparent,
        borderRadius: BorderRadius.circular(borderRadius),
        child: InkWell(
          borderRadius: BorderRadius.circular(borderRadius),
          onTap: onTap,
          onLongPress: onLongPress,
          child: Padding(
            padding: EdgeInsets.all(isTablet ? 12.0 : 8.0),
            child: Column(
              children: [
                // 이미지 영역
                Expanded(
                  child: Stack(
                    children: [
                      // 이미지 컨테이너 - 정사각형 유지
                      AspectRatio(
                        aspectRatio: 1.0, // 정사각형 비율 강제
                        child: Container(
                          decoration: BoxDecoration(
                            gradient: AppColors.accentGradient,
                            borderRadius: BorderRadius.circular(borderRadius * 0.7),
                          ),
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(borderRadius * 0.7),
                            child: Container(
                              decoration: BoxDecoration(
                                gradient: AppColors.accentGradient,
                              ),
                              child: Icon(
                                Icons.local_offer_rounded,
                                size: isTablet ? 56.0 : 48.0,
                                color: AppColors.onboardingTextOnPrimary,
                              ),
                            ),
                          ),
                        ),
                      ),

                      // 선택 오버레이 및 수량 표시
                      if (isSelected)
                        Positioned.fill(
                          child: Container(
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                begin: Alignment.topLeft,
                                end: Alignment.bottomRight,
                                colors: [
                                  AppColors.onboardingAccent.withValues(alpha: 0.7),
                                  AppColors.onboardingAccentLight.withValues(alpha: 0.5),
                                ],
                              ),
                              borderRadius: BorderRadius.circular(borderRadius * 0.7),
                            ),
                            child: Center(
                              child: Container(
                                padding: EdgeInsets.symmetric(
                                  horizontal: isTablet ? 20.0 : 16.0,
                                  vertical: isTablet ? 12.0 : 8.0,
                                ),
                                decoration: BoxDecoration(
                                  gradient: AppColors.accentGradient,
                                  borderRadius: BorderRadius.circular(borderRadius * 0.5),
                                  boxShadow: [
                                    BoxShadow(
                                      color: AppColors.onboardingAccent.withValues(alpha: 0.4),
                                      blurRadius: 8,
                                      offset: const Offset(0, 4),
                                    ),
                                  ],
                                ),
                                child: Text(
                                  quantity.toString(),
                                  style: TextStyle(
                                    color: AppColors.onboardingTextOnPrimary,
                                    fontSize: isTablet ? 24.0 : 20.0,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ),
                    ],
                  ),
                ),

                // 할인명
                Container(
                  margin: EdgeInsets.only(top: isTablet ? 12.0 : 8.0),
                  child: Text(
                    sale.name ?? '',
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: isTablet ? 18.0 : 16.0,
                      fontWeight: FontWeight.bold,
                      color: isSelected
                        ? AppColors.onboardingAccent
                        : AppColors.onboardingTextPrimary,
                    ),
                  ),
                ),

                SizedBox(height: isTablet ? 8.0 : 4.0),

                // 할인 금액 - 이미지 영역과 동일한 너비로 제한
                LayoutBuilder(
                  builder: (context, constraints) {
                    final imageWidth = constraints.maxWidth;
                    return SizedBox(
                      width: imageWidth,
                      child: Container(
                        padding: EdgeInsets.symmetric(
                          horizontal: isTablet ? 12.0 : 10.0,
                          vertical: isTablet ? 6.0 : 4.0,
                        ),
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            colors: [
                              AppColors.onboardingAccent.withValues(alpha: 0.2),
                              AppColors.onboardingAccentLight.withValues(alpha: 0.1),
                            ],
                          ),
                          borderRadius: BorderRadius.circular(borderRadius * 0.4),
                        ),
                        child: Text(
                          '-${CurrencyUtils.formatCurrency(sale.discountAmount)}',
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            fontSize: isTablet ? 16.0 : 14.0,
                            color: AppColors.onboardingAccent,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
