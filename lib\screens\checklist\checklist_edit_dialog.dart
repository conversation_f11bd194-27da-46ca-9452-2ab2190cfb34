import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../providers/checklist_provider.dart';
import '../../models/checklist_template.dart';
import '../../utils/app_colors.dart';
import '../../utils/dimens.dart';
import '../../utils/logger_utils.dart';
import '../../utils/toast_utils.dart';

/// 체크리스트 편집 다이얼로그
/// 
/// 체크리스트 템플릿을 추가, 수정, 삭제할 수 있는 다이얼로그입니다.
/// - 템플릿 목록 표시
/// - 새 템플릿 추가
/// - 기존 템플릿 수정/삭제
/// - 드래그 앤 드롭으로 순서 변경
class ChecklistEditDialog extends ConsumerStatefulWidget {
  const ChecklistEditDialog({super.key});

  @override
  ConsumerState<ChecklistEditDialog> createState() => _ChecklistEditDialogState();
}

class _ChecklistEditDialogState extends ConsumerState<ChecklistEditDialog> {
  static const String _tag = 'ChecklistEditDialog';
  
  final _titleController = TextEditingController();
  final _formKey = GlobalKey<FormState>();

  ChecklistTemplate? _editingTemplate;
  bool _isAdding = false;

  @override
  void dispose() {
    _titleController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final checklistState = ref.watch(checklistNotifierProvider);
    
    return Dialog(
      backgroundColor: AppColors.surface,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        height: MediaQuery.of(context).size.height * 0.8,
        padding: const EdgeInsets.all(Dimens.space20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 헤더
            Row(
              children: [
                Text(
                  '체크리스트 편집',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.w600,
                    color: AppColors.onSurface,
                  ),
                ),
                const Spacer(),
                IconButton(
                  icon: const Icon(Icons.close, color: AppColors.onSurfaceVariant),
                  onPressed: () => Navigator.of(context).pop(),
                ),
              ],
            ),
            const SizedBox(height: Dimens.space16),
            
            // 새 항목 추가 버튼
            if (!_isAdding && _editingTemplate == null)
              SizedBox(
                width: double.infinity,
                child: ElevatedButton.icon(
                  onPressed: () => _startAdding(),
                  icon: const Icon(Icons.add),
                  label: const Text('새 체크리스트 추가'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primarySeed,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: Dimens.space12),
                  ),
                ),
              ),
            
            // 추가/편집 폼
            if (_isAdding || _editingTemplate != null) ...[
              const SizedBox(height: Dimens.space16),
              _buildEditForm(),
            ],
            
            const SizedBox(height: Dimens.space16),
            
            // 구분선
            Container(
              height: 1,
              color: AppColors.neutral30,
            ),
            
            const SizedBox(height: Dimens.space16),
            
            // 기존 템플릿 목록
            Text(
              '기존 체크리스트',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: AppColors.onSurface,
              ),
            ),
            const SizedBox(height: Dimens.space12),
            
            Expanded(
              child: _buildTemplateList(checklistState),
            ),
          ],
        ),
      ),
    );
  }

  /// 편집 폼 위젯
  Widget _buildEditForm() {
    return Form(
      key: _formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 제목 입력
          TextFormField(
            controller: _titleController,
            decoration: InputDecoration(
              labelText: '체크리스트 제목',
              hintText: '예: 부스 설치 완료',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: const BorderSide(color: AppColors.primarySeed),
              ),
            ),
            validator: (value) {
              if (value == null || value.trim().isEmpty) {
                return '제목을 입력해주세요';
              }
              return null;
            },
            maxLength: 50,
          ),
          const SizedBox(height: Dimens.space16),
          
          // 버튼들
          Row(
            children: [
              Expanded(
                child: OutlinedButton(
                  onPressed: () => _cancelEdit(),
                  style: OutlinedButton.styleFrom(
                    side: const BorderSide(color: AppColors.neutral30),
                    padding: const EdgeInsets.symmetric(vertical: Dimens.space12),
                  ),
                  child: const Text('취소'),
                ),
              ),
              const SizedBox(width: Dimens.space12),
              Expanded(
                child: ElevatedButton(
                  onPressed: () => _saveTemplate(),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primarySeed,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: Dimens.space12),
                  ),
                  child: Text(_editingTemplate != null ? '수정' : '추가'),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 템플릿 목록 위젯
  Widget _buildTemplateList(ChecklistState state) {
    if (state.isLoading) {
      return const Center(
        child: CircularProgressIndicator(
          valueColor: AlwaysStoppedAnimation<Color>(AppColors.primarySeed),
        ),
      );
    }

    if (state.templates.isEmpty) {
      return Center(
        child: Text(
          '등록된 체크리스트가 없습니다',
          style: TextStyle(
            fontSize: 14,
            color: AppColors.onSurfaceVariant,
          ),
        ),
      );
    }

    return ListView.builder(
      itemCount: state.templates.length,
      itemBuilder: (context, index) {
        final template = state.templates[index];
        return _buildTemplateItem(template);
      },
    );
  }

  /// 템플릿 아이템 위젯
  Widget _buildTemplateItem(ChecklistTemplate template) {
    final isEditing = _editingTemplate?.id == template.id;
    
    return Container(
      margin: const EdgeInsets.only(bottom: Dimens.space8),
      decoration: BoxDecoration(
        color: isEditing ? AppColors.primarySeed.withValues(alpha: 0.1) : AppColors.background,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: isEditing ? AppColors.primarySeed : AppColors.neutral30,
          width: isEditing ? 2 : 1,
        ),
      ),
      child: ListTile(
        title: Text(
          template.title,
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: AppColors.onSurface,
          ),
        ),

        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            IconButton(
              icon: const Icon(Icons.edit, size: 20),
              onPressed: () => _startEditing(template),
              color: AppColors.primarySeed,
            ),
            IconButton(
              icon: const Icon(Icons.delete, size: 20),
              onPressed: () => _deleteTemplate(template),
              color: AppColors.error,
            ),
          ],
        ),
      ),
    );
  }

  /// 새 항목 추가 시작
  void _startAdding() {
    setState(() {
      _isAdding = true;
      _editingTemplate = null;
      _titleController.clear();
    });
  }

  /// 기존 항목 편집 시작
  void _startEditing(ChecklistTemplate template) {
    setState(() {
      _isAdding = false;
      _editingTemplate = template;
      _titleController.text = template.title;
    });
  }

  /// 편집 취소
  void _cancelEdit() {
    setState(() {
      _isAdding = false;
      _editingTemplate = null;
      _titleController.clear();
    });
  }

  /// 템플릿 저장
  void _saveTemplate() async {
    if (!_formKey.currentState!.validate()) return;

    try {
      final title = _titleController.text.trim();

      if (_editingTemplate != null) {
        // 수정
        final updatedTemplate = _editingTemplate!.copyWith(
          title: title,
          updatedAt: DateTime.now(),
        );
        
        await ref.read(checklistNotifierProvider.notifier).updateTemplate(updatedTemplate);
        LoggerUtils.logInfo('체크리스트 템플릿 수정 완료: $title', tag: _tag);
      } else {
        // 추가
        final newTemplate = ChecklistTemplate.create(
          title: title,
        );
        
        await ref.read(checklistNotifierProvider.notifier).addTemplate(newTemplate);
        LoggerUtils.logInfo('체크리스트 템플릿 추가 완료: $title', tag: _tag);
      }

      _cancelEdit();
      
      if (mounted) {
        ToastUtils.showSuccess(
          context,
          _editingTemplate != null ? '체크리스트가 수정되었습니다' : '체크리스트가 추가되었습니다',
        );
      }
    } catch (e) {
      LoggerUtils.logError('체크리스트 템플릿 저장 실패', tag: _tag, error: e);
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('저장에 실패했습니다: ${e.toString()}'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  /// 템플릿 삭제
  void _deleteTemplate(ChecklistTemplate template) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('체크리스트 삭제'),
        content: Text('\'${template.title}\'을(를) 삭제하시겠습니까?\n\n모든 행사의 체크 기록도 함께 삭제됩니다.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('취소'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: AppColors.error),
            child: const Text('삭제'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await ref.read(checklistNotifierProvider.notifier).deleteTemplate(template.id!);
        LoggerUtils.logInfo('체크리스트 템플릿 삭제 완료: ${template.title}', tag: _tag);
        
        if (mounted) {
          ToastUtils.showSuccess(
            context,
            '체크리스트가 삭제되었습니다',
          );
        }
      } catch (e) {
        LoggerUtils.logError('체크리스트 템플릿 삭제 실패', tag: _tag, error: e);
        
        if (mounted) {
          ToastUtils.showError(
            context,
            '삭제에 실패했습니다: ${e.toString()}',
          );
        }
      }
    }
  }
}
