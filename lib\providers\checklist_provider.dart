import 'dart:async';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import '../models/checklist_template.dart';
import '../models/checklist_item.dart';
import '../models/event_workspace.dart';
import '../repositories/checklist_repository.dart';
import '../utils/logger_utils.dart';
import '../services/database_service.dart';
import 'unified_workspace_provider.dart';

import '../services/realtime_sync_service_main.dart';
import 'data_sync_provider.dart';

/// 체크리스트 상태를 관리하는 State 클래스
class ChecklistState {
  final List<ChecklistTemplate> templates;
  final List<ChecklistItem> items;
  final bool isLoading;
  final String? error;
  final bool isUpdating;

  const ChecklistState({
    this.templates = const [],
    this.items = const [],
    this.isLoading = false,
    this.error,
    this.isUpdating = false,
  });

  ChecklistState copyWith({
    List<ChecklistTemplate>? templates,
    List<ChecklistItem>? items,
    bool? isLoading,
    String? error,
    bool? isUpdating,
  }) {
    return ChecklistState(
      templates: templates ?? this.templates,
      items: items ?? this.items,
      isLoading: isLoading ?? this.isLoading,
      error: error ?? this.error,
      isUpdating: isUpdating ?? this.isUpdating,
    );
  }

  static ChecklistState initialState() => const ChecklistState();
}

/// 체크리스트 상태를 관리하는 Provider
class ChecklistNotifier extends StateNotifier<ChecklistState> {
  static const String _tag = 'ChecklistNotifier';
  
  final ChecklistRepository repository;
  final Ref ref;
  StreamSubscription<RealtimeDataChange>? _realtimeSubscription;
  StreamSubscription<QuerySnapshot>? _firestoreSubscription;
  final Set<int> _recentlyAddedTemplates = {};
  final Set<int> _recentlyDeletedTemplates = {};
  bool _isInitialized = false;

  ChecklistNotifier(this.repository, this.ref) : super(ChecklistState.initialState()) {
    _setupDirectFirestoreSync();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      final currentWorkspace = ref.read(currentWorkspaceProvider);
      if (currentWorkspace != null) {
        _isInitialized = true;
        loadData(showLoading: false);
        _watchCurrentEvent(); // 초기 로드 후에 workspace 변경 감지 시작
      }
    });
  }

  @override
  void dispose() {
    _realtimeSubscription?.cancel();
    _firestoreSubscription?.cancel();
    super.dispose();
  }

  /// 현재 행사 변경 감지
  void _watchCurrentEvent() {
    ref.listen<EventWorkspace?>(currentWorkspaceProvider, (previous, next) {
      if (_isInitialized && next != null && previous?.id != next.id) {
        LoggerUtils.logInfo('행사 변경 감지 - 체크리스트 데이터 로드: ${next.name}', tag: _tag);
        loadData();
      }
    });
  }

  /// 직접 Firestore 실시간 동기화 설정
  void _setupDirectFirestoreSync() {
    final user = FirebaseAuth.instance.currentUser;
    if (user == null) return;

    // 체크리스트 템플릿 실시간 동기화
    _firestoreSubscription = FirebaseFirestore.instance
        .collection('users')
        .doc(user.uid)
        .collection('checklist_templates')
        .snapshots()
        .listen(_handleFirestoreTemplateChanges);
  }

  /// Firestore 체크리스트 템플릿 변경사항 처리
  void _handleFirestoreTemplateChanges(QuerySnapshot snapshot) async {
    try {
      for (final change in snapshot.docChanges) {
        final data = change.doc.data() as Map<String, dynamic>?;
        if (data == null) continue;

        final template = ChecklistTemplate.fromJson(data);
        
        // 최근 추가한 템플릿은 무시 (무한 루프 방지)
        if (template.id != null && _recentlyAddedTemplates.contains(template.id!)) {
          continue;
        }

        switch (change.type) {
          case DocumentChangeType.added:
          case DocumentChangeType.modified:
            await _syncTemplateToLocalSafe(template);
            break;
          case DocumentChangeType.removed:
            if (template.id != null && !_recentlyDeletedTemplates.contains(template.id!)) {
              await repository.deleteTemplate(template.id!);
            }
            break;
        }
      }

      // 로컬 데이터 다시 로드
      await loadData(showLoading: false);
    } catch (e) {
      LoggerUtils.logError('Firestore 체크리스트 변경사항 처리 실패', tag: _tag, error: e);
    }
  }

  /// 체크리스트 템플릿을 안전하게 로컬 DB에 동기화
  Future<void> _syncTemplateToLocalSafe(ChecklistTemplate template) async {
    try {
      final existingTemplates = await repository.getAllTemplates();
      final existingTemplate = existingTemplates.where((t) => t.id == template.id).firstOrNull;
      
      if (existingTemplate != null) {
        await repository.updateTemplate(template);
      } else {
        await repository.insertTemplate(template);
      }
      
      LoggerUtils.logDebug('체크리스트 템플릿 안전 동기화 완료: ${template.title}', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('체크리스트 템플릿 안전 동기화 실패: ${template.title}', tag: _tag, error: e);
      rethrow;
    }
  }

  /// 체크리스트 데이터 로드
  Future<void> loadData({bool showLoading = true}) async {
    final currentWorkspace = ref.read(currentWorkspaceProvider);
    if (currentWorkspace == null) {
      LoggerUtils.logWarning('현재 워크스페이스가 없어 체크리스트 로드 불가', tag: _tag);
      state = state.copyWith(
        isLoading: false,
        error: '워크스페이스가 설정되지 않았습니다. 행사를 먼저 선택해주세요.',
        templates: [],
        items: [],
      );
      return;
    }

    if (showLoading) {
      state = state.copyWith(isLoading: true, error: null);
    }

    try {
      LoggerUtils.logInfo('체크리스트 데이터 로드 시작 - 워크스페이스: ${currentWorkspace.name}', tag: _tag);

      // 1단계: 로컬에서 템플릿 로드
      List<ChecklistTemplate> templates = await repository.getAllTemplates();

      // 2단계: 로컬에 템플릿이 없으면 서버에서 가져오기
      if (templates.isEmpty) {
        LoggerUtils.logInfo('로컬에 체크리스트 템플릿이 없음 - 서버에서 데이터 가져오기 시도', tag: _tag);

        try {
          final dataSyncService = ref.read(dataSyncServiceProvider);
          await dataSyncService.downloadChecklistTemplatesFromFirebase();
          templates = await repository.getAllTemplates();
          LoggerUtils.logInfo('서버에서 체크리스트 템플릿 다운로드 완료: ${templates.length}개', tag: _tag);
        } catch (e) {
          LoggerUtils.logWarning('서버에서 체크리스트 템플릿 가져오기 실패', tag: _tag, error: e);
        }
      }

      // 3단계: 현재 행사의 체크리스트 아이템 로드
      final items = await repository.getItemsByEventId(currentWorkspace.id);

      state = state.copyWith(
        templates: templates,
        items: items,
        isLoading: false,
        error: null,
      );

      LoggerUtils.logInfo('체크리스트 데이터 로드 완료 - 템플릿: ${templates.length}개, 아이템: ${items.length}개', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('체크리스트 데이터 로드 실패', tag: _tag, error: e);
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  /// 체크리스트 템플릿 추가
  Future<void> addTemplate(ChecklistTemplate template) async {
    try {
      state = state.copyWith(isUpdating: true);

      LoggerUtils.logInfo('체크리스트 템플릿 등록 시작: ${template.title}', tag: _tag);

      // 1. 로컬 DB에 저장
      await repository.insertTemplate(template);
      LoggerUtils.logInfo('체크리스트 템플릿 로컬 저장 성공: ${template.title}', tag: _tag);

      // 최근 추가한 템플릿으로 캐시 (무한 루프 방지용)
      if (template.id != null) {
        _recentlyAddedTemplates.add(template.id!);
        Future.delayed(const Duration(seconds: 5), () {
          _recentlyAddedTemplates.remove(template.id!);
        });
      }

      // 2. Firebase에 즉시 업로드
      try {
        final dataSyncService = ref.read(dataSyncServiceProvider);
        await dataSyncService.uploadSingleChecklistTemplate(template);
        LoggerUtils.logInfo('체크리스트 템플릿 Firebase 업로드 성공: ${template.title}', tag: _tag);
      } catch (e) {
        LoggerUtils.logError('체크리스트 템플릿 Firebase 업로드 실패: ${template.title}', tag: _tag, error: e);
      }

      // 3. 로컬 데이터 다시 로드
      await loadData(showLoading: false);
      
      state = state.copyWith(isUpdating: false);
    } catch (e) {
      LoggerUtils.logError('체크리스트 템플릿 추가 실패', tag: _tag, error: e);
      state = state.copyWith(isUpdating: false, error: e.toString());
      rethrow;
    }
  }

  /// 체크리스트 템플릿 수정
  Future<void> updateTemplate(ChecklistTemplate template) async {
    try {
      state = state.copyWith(isUpdating: true);

      LoggerUtils.logInfo('체크리스트 템플릿 수정 시작: ${template.title}', tag: _tag);

      // 1. 로컬 DB 업데이트
      await repository.updateTemplate(template);

      // 2. Firebase에 즉시 업로드
      try {
        final dataSyncService = ref.read(dataSyncServiceProvider);
        await dataSyncService.uploadSingleChecklistTemplate(template);
        LoggerUtils.logInfo('체크리스트 템플릿 Firebase 업데이트 성공: ${template.title}', tag: _tag);
      } catch (e) {
        LoggerUtils.logError('체크리스트 템플릿 Firebase 업데이트 실패: ${template.title}', tag: _tag, error: e);
      }

      // 3. 로컬 데이터 다시 로드
      await loadData(showLoading: false);
      
      state = state.copyWith(isUpdating: false);
    } catch (e) {
      LoggerUtils.logError('체크리스트 템플릿 수정 실패', tag: _tag, error: e);
      state = state.copyWith(isUpdating: false, error: e.toString());
      rethrow;
    }
  }

  /// 체크리스트 템플릿 삭제
  Future<void> deleteTemplate(int templateId) async {
    try {
      state = state.copyWith(isUpdating: true);

      LoggerUtils.logInfo('체크리스트 템플릿 삭제 시작: $templateId', tag: _tag);

      // 최근 삭제한 템플릿으로 캐시 (무한 루프 방지용)
      _recentlyDeletedTemplates.add(templateId);
      Future.delayed(const Duration(seconds: 5), () {
        _recentlyDeletedTemplates.remove(templateId);
      });

      // 1. 로컬 DB에서 삭제
      await repository.deleteTemplate(templateId);
      await repository.deleteItemsByTemplateId(templateId);

      // 2. Firebase에서 삭제
      try {
        final dataSyncService = ref.read(dataSyncServiceProvider);
        await dataSyncService.deleteChecklistTemplate(templateId);
        LoggerUtils.logInfo('체크리스트 템플릿 Firebase 삭제 성공: $templateId', tag: _tag);
      } catch (e) {
        LoggerUtils.logError('체크리스트 템플릿 Firebase 삭제 실패: $templateId', tag: _tag, error: e);
      }

      // 3. 로컬 데이터 다시 로드
      await loadData(showLoading: false);
      
      state = state.copyWith(isUpdating: false);
    } catch (e) {
      LoggerUtils.logError('체크리스트 템플릿 삭제 실패', tag: _tag, error: e);
      state = state.copyWith(isUpdating: false, error: e.toString());
      rethrow;
    }
  }

  /// 체크리스트 아이템 체크 토글
  Future<void> toggleItemCheck(int templateId) async {
    final currentWorkspace = ref.read(currentWorkspaceProvider);
    if (currentWorkspace == null) return;

    try {
      LoggerUtils.logInfo('체크리스트 아이템 체크 토글: templateId=$templateId, eventId=${currentWorkspace.id}', tag: _tag);

      // 1. 로컬 DB 업데이트
      await repository.toggleItemCheck(templateId, currentWorkspace.id);

      // 2. Firebase에 동기화
      try {
        final items = await repository.getItemsByEventId(currentWorkspace.id);
        final item = items.where((i) => i.templateId == templateId).firstOrNull;
        if (item != null) {
          final dataSyncService = ref.read(dataSyncServiceProvider);
          await dataSyncService.uploadSingleChecklistItem(item);
        }
      } catch (e) {
        LoggerUtils.logError('체크리스트 아이템 Firebase 동기화 실패', tag: _tag, error: e);
      }

      // 3. 로컬 데이터 다시 로드
      await loadData(showLoading: false);
    } catch (e) {
      LoggerUtils.logError('체크리스트 아이템 체크 토글 실패', tag: _tag, error: e);
      rethrow;
    }
  }

  /// 특정 템플릿의 체크 상태 조회
  bool isTemplateChecked(int templateId) {
    final currentWorkspace = ref.read(currentWorkspaceProvider);
    if (currentWorkspace == null) return false;

    return state.items.any((item) => 
        item.templateId == templateId && 
        item.eventId == currentWorkspace.id && 
        item.isChecked);
  }
}

// Provider 정의
final checklistRepositoryProvider = Provider<ChecklistRepository>((ref) {
  final databaseService = ref.read(databaseServiceProvider);
  return ChecklistRepository(databaseService);
});

final checklistNotifierProvider = StateNotifierProvider<ChecklistNotifier, ChecklistState>((ref) {
  final repository = ref.read(checklistRepositoryProvider);
  return ChecklistNotifier(repository, ref);
});
