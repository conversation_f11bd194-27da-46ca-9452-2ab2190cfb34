import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:image_picker/image_picker.dart';
import 'package:syncfusion_flutter_datepicker/datepicker.dart';
import 'dart:io';
import 'package:image/image.dart' as img;
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;

import '../../models/event.dart';
import '../../providers/event_provider.dart';
import '../../providers/unified_workspace_provider.dart';
import '../../utils/event_workspace_utils.dart';
import '../../utils/logger_utils.dart';
import '../../utils/app_colors.dart';
import '../../utils/responsive_helper.dart';
import '../../widgets/onboarding_components.dart';
import '../../widgets/loading_indicator.dart';
import '../../widgets/image_crop_widget.dart';
import '../../utils/image_utils.dart';

/// 행사 생성/수정 화면
/// 
/// 새로운 행사를 생성하거나 기존 행사를 수정하는 폼 화면
class EventFormScreen extends ConsumerStatefulWidget {
  final Event? event; // null이면 새 행사 생성, 값이 있으면 수정

  const EventFormScreen({super.key, this.event});

  @override
  ConsumerState<EventFormScreen> createState() => _EventFormScreenState();
}

class _EventFormScreenState extends ConsumerState<EventFormScreen> {
  static const String _tag = 'EventFormScreen';
  
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();

  DateTime? _startDate;
  DateTime? _endDate;
  String? _imagePath;
  bool _isLoading = false;

  bool get _isEditing => widget.event != null;

  @override
  void initState() {
    super.initState();
    LoggerUtils.methodStart('initState', tag: _tag);
    
    if (_isEditing) {
      _initializeWithExistingEvent();
    } else {
      _initializeForNewEvent();
    }
    
    LoggerUtils.methodEnd('initState', tag: _tag);
  }

  /// 기존 행사 정보로 초기화
  void _initializeWithExistingEvent() {
    final event = widget.event!;
    _nameController.text = event.name;
    _descriptionController.text = event.description ?? '';
    _startDate = event.startDate;
    _endDate = event.endDate;
    _imagePath = event.imagePath;
  }

  /// 새 행사를 위한 초기화
  void _initializeForNewEvent() {
    // 새 행사 생성시에는 날짜 초기값을 설정하지 않음
    _startDate = null;
    _endDate = null;
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: _buildAppBar(),
      body: _buildBody(),
    );
  }

  /// AppBar 구성
  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: Text(
        _isEditing ? '행사 수정' : '새 행사',
        style: TextStyle(
          fontSize: ResponsiveHelper.getSubtitleFontSize(context),
          fontWeight: FontWeight.bold,
          color: AppColors.onboardingTextPrimary,
        ),
      ),
      backgroundColor: AppColors.surface,
      elevation: 0,
      iconTheme: IconThemeData(color: AppColors.onboardingTextPrimary),
    );
  }

  /// 메인 바디 구성
  Widget _buildBody() {
    if (_isLoading) {
      return const Center(child: LoadingIndicator());
    }

    return Center(
      child: SingleChildScrollView(
        child: OnboardingComponents.buildCard(
          context: context,
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 헤더
                _buildHeader(),

                OnboardingComponents.buildSectionSpacing(context),

                // 이미지와 입력 필드가 나란히 배치되는 섹션
                _buildImageAndInputSection(),

                OnboardingComponents.buildSectionSpacing(context),

                // 날짜 선택 섹션
                _buildDateSection(),

                OnboardingComponents.buildSectionSpacing(context),

                // 저장 버튼
                _buildSaveButton(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 헤더 섹션
  Widget _buildHeader() {
    return Column(
      children: [
        // 제목
        OnboardingComponents.buildTitle(
          context: context,
          text: _isEditing ? '행사 수정' : '새 행사 등록',
        ),

        const SizedBox(height: 6),

        // 부제목
        OnboardingComponents.buildSubtitle(
          context: context,
          text: _isEditing ? '행사 정보를 수정하세요' : '새로운 행사를 등록하세요',
        ),
      ],
    );
  }

  /// 이미지와 입력 필드 섹션 (나란히 배치)
  Widget _buildImageAndInputSection() {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        // 왼쪽: 이미지 추가
        _buildImageCropSection(),
        
        const SizedBox(width: 16),
        
        // 오른쪽: 행사명과 설명 입력
        Expanded(
          child: Column(
            children: [
              // 행사명 입력
              OnboardingComponents.buildTextField(
                context: context,
                controller: _nameController,
                label: '행사명',
                hint: '행사 이름을 입력하세요',
                prefixIcon: Icons.event,
                textInputAction: TextInputAction.next,
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return '행사명을 입력해주세요';
                  }
                  return null;
                },
              ),
              
              const SizedBox(height: 12),
              
              // 메모 입력
              OnboardingComponents.buildTextField(
                context: context,
                controller: _descriptionController,
                label: '메모',
                hint: '행사에 대한 메모를 입력하세요',
                prefixIcon: Icons.description,
                textInputAction: TextInputAction.done,
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// 이미지 크롭 섹션 (왼쪽 배치용)
  Widget _buildImageCropSection() {
    return GestureDetector(
      onTap: _pickAndCropImage,
      child: Container(
        width: 110,
        height: 110,
        decoration: BoxDecoration(
          color: AppColors.surfaceVariant,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: AppColors.onboardingPrimary.withValues(alpha: 0.3),
            width: 2,
          ),
        ),
        child: _imagePath != null
            ? ClipRRect(
                borderRadius: BorderRadius.circular(10),
                child: Image.file(
                  File(_imagePath!),
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) {
                    return _buildImagePlaceholder();
                  },
                ),
              )
            : _buildImagePlaceholder(),
      ),
    );
  }

  /// 이미지 선택 및 크롭
  Future<void> _pickAndCropImage() async {
    try {
      final ImagePicker picker = ImagePicker();
      final XFile? image = await picker.pickImage(
        source: ImageSource.gallery,
        imageQuality: 85,
      );

      if (image != null) {
        // 1. 3단계 방식: 흰색 800x800 캔버스 + 이미지 최대 650px로 전처리
        final originalBytes = await image.readAsBytes();
        final paddedBytes = await addWhitePaddingAndCenterImage(originalBytes);
        final tempDir = await Directory.systemTemp.createTemp('event_crop_temp');
        final tempFile = File('${tempDir.path}/padded_${DateTime.now().millisecondsSinceEpoch}.jpg');
        await tempFile.writeAsBytes(paddedBytes);

        // 2. 크롭 다이얼로그 호출 (흰색 패딩 포함 이미지)
        final croppedFile = await ImageCropUtils.cropImage(
          context: context,
          imagePath: tempFile.path,
          shape: CropShape.roundedSquare,
          aspectRatio: 1.0,
        );

        if (croppedFile != null) {
          // 3. 크롭된 이미지를 200x200으로 리사이즈 (프로필 이미지와 동일)
          final croppedBytes = await croppedFile.readAsBytes();
          final img.Image? imgDecoded = img.decodeImage(croppedBytes);
          final img.Image imgResized = img.copyResize(imgDecoded!, width: 200, height: 200);
          final jpgBytes = img.encodeJpg(imgResized, quality: 80);

          // 4. 영구 저장소에 최종 파일 저장
          final appDocDir = await getApplicationDocumentsDirectory();
          final eventImagesDir = Directory(path.join(appDocDir.path, 'event_images'));
          if (!await eventImagesDir.exists()) {
            await eventImagesDir.create(recursive: true);
          }

          final timestamp = DateTime.now().millisecondsSinceEpoch;
          final fileName = 'event_${timestamp}.jpg';
          final finalFile = File(path.join(eventImagesDir.path, fileName));
          await finalFile.writeAsBytes(jpgBytes);

          setState(() {
            _imagePath = finalFile.path;
          });
        }
      }
    } catch (e) {
      LoggerUtils.logError('이미지 선택 실패', tag: _tag, error: e);
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('이미지 선택에 실패했습니다')),
        );
      }
    }
  }

  /// 이미지 플레이스홀더 (작은 크기에 맞게 조정)
  Widget _buildImagePlaceholder() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Icon(
          Icons.add_photo_alternate_outlined,
          size: 32,
          color: AppColors.onboardingPrimary,
        ),
        const SizedBox(height: 4),
        Text(
          '이미지 추가',
          style: TextStyle(
            fontSize: 10,
            color: AppColors.onboardingPrimary,
            fontWeight: FontWeight.w600,
          ),
        ),
      ],
    );
  }

  /// 날짜 섹션
  Widget _buildDateSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        OnboardingComponents.buildSubtitle(
          context: context,
          text: '행사 기간 *',
          textAlign: TextAlign.left,
          color: AppColors.onboardingTextPrimary,
        ),
        const SizedBox(height: 12),

        GestureDetector(
          onTap: () {
            // 키보드 숨기기
            FocusScope.of(context).unfocus();
            _showDateRangePicker();
          },
          child: Container(
            width: double.infinity,
            padding: ResponsiveHelper.getButtonPadding(context),
            decoration: BoxDecoration(
              color: AppColors.surfaceVariant,
              borderRadius: BorderRadius.circular(ResponsiveHelper.getBorderRadius(context)),
              border: Border.all(color: AppColors.secondary),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.calendar_month,
                  color: AppColors.onboardingPrimary,
                  size: ResponsiveHelper.getIconSize(context),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    _getDateRangeText(),
                    style: TextStyle(
                      fontSize: ResponsiveHelper.getBodyFontSize(context),
                      color: AppColors.onboardingTextPrimary,
                    ),
                  ),
                ),
                Icon(
                  Icons.arrow_drop_down,
                  color: AppColors.onboardingTextSecondary,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }





  /// 날짜 범위 선택 다이얼로그 표시
  Future<void> _showDateRangePicker() async {
    await showDialog(
      context: context,
      builder: (context) {
        return Dialog(
          backgroundColor: AppColors.surface,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(ResponsiveHelper.getBorderRadius(context) * 1.5),
          ),
          child: Container(
            padding: ResponsiveHelper.getCardPadding(context),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // 헤더
                Row(
                  children: [
                    Text(
                      '행사 기간 선택',
                      style: TextStyle(
                        fontSize: ResponsiveHelper.getSubtitleFontSize(context),
                        fontWeight: FontWeight.w600,
                        color: AppColors.onboardingTextPrimary,
                      ),
                    ),
                    const Spacer(),
                    IconButton(
                      onPressed: () => Navigator.of(context).pop(),
                      icon: Icon(
                        Icons.close,
                        color: AppColors.onboardingTextSecondary,
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 16),

                // 날짜 범위 선택기
                Container(
                  height: 350,
                  decoration: BoxDecoration(
                    color: AppColors.surfaceVariant,
                    borderRadius: BorderRadius.circular(ResponsiveHelper.getBorderRadius(context)),
                  ),
                  child: SfDateRangePicker(
                    onSelectionChanged: _onDateRangeSelectionChanged,
                    selectionMode: DateRangePickerSelectionMode.range,
                    initialSelectedRange: _startDate != null && _endDate != null
                        ? PickerDateRange(_startDate, _endDate)
                        : null,
                    minDate: DateTime.now(),
                    maxDate: DateTime.now().add(const Duration(days: 365)),
                    monthViewSettings: const DateRangePickerMonthViewSettings(
                      firstDayOfWeek: 1, // 월요일부터 시작
                      dayFormat: 'EEE',
                      weekNumberStyle: DateRangePickerWeekNumberStyle(),
                    ),
                    selectionColor: AppColors.onboardingPrimary,
                    rangeSelectionColor: AppColors.primaryOverlay,
                    startRangeSelectionColor: AppColors.onboardingPrimary,
                    endRangeSelectionColor: AppColors.onboardingPrimary,
                    todayHighlightColor: AppColors.onboardingAccent,
                    headerStyle: DateRangePickerHeaderStyle(
                      textStyle: TextStyle(
                        color: AppColors.onboardingTextPrimary,
                        fontSize: ResponsiveHelper.getBodyFontSize(context),
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    monthCellStyle: DateRangePickerMonthCellStyle(
                      textStyle: TextStyle(
                        color: AppColors.onboardingTextPrimary,
                        fontSize: ResponsiveHelper.getBodyFontSize(context) - 2,
                      ),
                      disabledDatesTextStyle: TextStyle(
                        color: AppColors.onboardingTextTertiary,
                        fontSize: ResponsiveHelper.getBodyFontSize(context) - 2,
                      ),
                    ),
                  ),
                ),

                const SizedBox(height: 16),

                // 확인 버튼
                OnboardingComponents.buildPrimaryButton(
                  context: context,
                  text: '확인',
                  onPressed: () {
                    // 키보드 숨기기
                    FocusScope.of(context).unfocus();
                    Navigator.of(context).pop();
                  },
                  icon: Icons.check,
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  /// 날짜 범위 선택 변경 처리
  void _onDateRangeSelectionChanged(DateRangePickerSelectionChangedArgs args) {
    if (args.value is PickerDateRange) {
      final PickerDateRange range = args.value;
      setState(() {
        _startDate = range.startDate;
        _endDate = range.endDate ?? range.startDate;
      });
    }
  }

  /// 저장 버튼
  Widget _buildSaveButton() {
    return OnboardingComponents.buildPrimaryButton(
      context: context,
      text: _isEditing ? '수정' : '생성',
      onPressed: _isLoading ? null : _saveEvent,
      isLoading: _isLoading,
      icon: _isEditing ? Icons.edit : Icons.add,
    );
  }

  /// 날짜 범위 텍스트 반환
  String _getDateRangeText() {
    if (_startDate == null || _endDate == null) {
      return '행사 기간을 선택하세요';
    }

    final startText = '${_startDate!.year}년 ${_startDate!.month}월 ${_startDate!.day}일';
    final endText = '${_endDate!.year}년 ${_endDate!.month}월 ${_endDate!.day}일';

    return '$startText ~ $endText';
  }







  /// 행사 저장
  Future<void> _saveEvent() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (_startDate == null || _endDate == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('행사 기간을 선택해주세요')),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      LoggerUtils.methodStart('_saveEvent', tag: _tag);
      
      final event = Event.create(
        id: _isEditing ? widget.event!.id : null,
        name: _nameController.text.trim(),
        imagePath: _imagePath,
        startDate: _startDate!,
        endDate: _endDate!,
        isActive: true, // 기본값으로 설정
        description: _descriptionController.text.trim().isEmpty
          ? null
          : _descriptionController.text.trim(),
      );

      if (_isEditing) {
        await ref.read(eventNotifierProvider.notifier).updateEvent(event);
      } else {
        final addedEvent = await ref.read(eventNotifierProvider.notifier).addEvent(event);
        
        // 새 행사 생성 후 자동으로 현재 행사로 설정하여 닉네임 기반 판매자 자동 생성 보장
        if (addedEvent != null) {
          LoggerUtils.logInfo('새 행사 생성 완료, 워크스페이스 전환 시작: ${addedEvent.name} (ID: ${addedEvent.id})', tag: _tag);
          try {
            final workspace = EventWorkspaceUtils.eventToWorkspace(addedEvent);
            if (workspace != null) {
              LoggerUtils.logInfo('EventWorkspace 변환 완료: ${workspace.name} (ID: ${workspace.id})', tag: _tag);
              await ref.read(unifiedWorkspaceProvider.notifier).switchToWorkspace(workspace);
              LoggerUtils.logInfo('워크스페이스 전환 완료', tag: _tag);
            } else {
              LoggerUtils.logError('EventWorkspace 변환 실패', tag: _tag);
            }
          } catch (workspaceError) {
            LoggerUtils.logError('워크스페이스 전환 중 오류 발생', tag: _tag, error: workspaceError);
            // 워크스페이스 전환 실패해도 행사 생성은 성공했으므로 계속 진행
          }
        } else {
          LoggerUtils.logError('새 행사 생성 실패', tag: _tag);
        }
      }

      if (mounted) {
        // 새 행사 생성 시에는 메인 화면으로 이동, 수정 시에는 이전 화면으로 돌아가기
        if (_isEditing) {
          Navigator.of(context).pop();
        } else {
          // 새 행사 생성 시 모든 이전 화면을 제거하고 메인 화면으로 이동
          Navigator.of(context).pushNamedAndRemoveUntil(
            '/',
            (route) => false,
          );
        }

        // 성공 메시지는 부모 화면에서 처리하도록 변경
      }
      
      LoggerUtils.methodEnd('_saveEvent', tag: _tag);
    } catch (e, stackTrace) {
      LoggerUtils.logError(
        '행사 저장 실패',
        tag: _tag,
        error: e,
        stackTrace: stackTrace,
      );
      
      if (mounted) {
        // 에러 메시지는 부모 화면에서 처리하도록 변경
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
