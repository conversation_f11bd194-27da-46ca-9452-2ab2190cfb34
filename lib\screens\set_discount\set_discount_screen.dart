import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../providers/set_discount_provider.dart';
import '../../models/set_discount.dart';
import '../../utils/currency_utils.dart';
import '../../utils/toast_utils.dart';
import '../../widgets/confirmation_dialog.dart';
import 'set_discount_form_dialog.dart';

/// 세트 할인 관리 화면
class SetDiscountScreen extends ConsumerStatefulWidget {
  const SetDiscountScreen({super.key});

  @override
  ConsumerState<SetDiscountScreen> createState() => _SetDiscountScreenState();
}

class _SetDiscountScreenState extends ConsumerState<SetDiscountScreen> {
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    // 화면 진입 시 데이터 로드
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(setDiscountNotifierProvider.notifier).loadSetDiscounts();
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final state = ref.watch(setDiscountNotifierProvider);
    final isLoading = ref.watch(setDiscountLoadingProvider);
    final isUpdating = ref.watch(setDiscountUpdatingProvider);
    final errorMessage = ref.watch(setDiscountErrorProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('세트 할인 관리'),
        centerTitle: true,
        actions: [
          // 필터 토글 버튼
          IconButton(
            icon: Icon(
              state.showActiveOnly ? Icons.visibility : Icons.visibility_off,
            ),
            tooltip: state.showActiveOnly ? '전체 보기' : '활성화만 보기',
            onPressed: () {
              ref.read(setDiscountNotifierProvider.notifier).toggleShowActiveOnly();
            },
          ),
          // 새로고침 버튼
          IconButton(
            icon: const Icon(Icons.refresh),
            tooltip: '새로고침',
            onPressed: () {
              ref.read(setDiscountNotifierProvider.notifier).loadSetDiscounts();
            },
          ),
        ],
      ),
      body: SafeArea(
        child: Column(
          children: [
            // 검색 바
            Padding(
              padding: const EdgeInsets.all(16),
              child: TextField(
                controller: _searchController,
                decoration: const InputDecoration(
                  hintText: '세트 이름으로 검색...',
                  prefixIcon: Icon(Icons.search),
                  border: OutlineInputBorder(),
                ),
                onChanged: (value) {
                  ref.read(setDiscountNotifierProvider.notifier).setSearchQuery(value);
                },
              ),
            ),

            // 에러 메시지 표시
            if (errorMessage != null)
              Container(
                margin: const EdgeInsets.symmetric(horizontal: 16),
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.red.shade50,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.red.shade200),
                ),
                child: Row(
                  children: [
                    Icon(Icons.error_outline, color: Colors.red.shade600),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        errorMessage,
                        style: TextStyle(color: Colors.red.shade700),
                      ),
                    ),
                    IconButton(
                      icon: const Icon(Icons.close),
                      onPressed: () {
                        ref.read(setDiscountNotifierProvider.notifier).clearError();
                      },
                    ),
                  ],
                ),
              ),

            // 세트 할인 목록
            Expanded(
              child: isLoading
                  ? const Center(child: CircularProgressIndicator())
                  : state.filteredSetDiscounts.isEmpty
                      ? _buildEmptyState()
                      : _buildSetDiscountList(state.filteredSetDiscounts),
            ),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: isUpdating ? null : _showAddDialog,
        tooltip: '세트 할인 추가',
        child: isUpdating
            ? const SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(strokeWidth: 2),
              )
            : const Icon(Icons.add),
      ),
    );
  }

  /// 빈 상태 위젯
  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.local_offer_outlined,
            size: 64,
            color: Colors.grey.shade400,
          ),
          const SizedBox(height: 16),
          Text(
            '등록된 세트 할인이 없습니다',
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey.shade600,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            '+ 버튼을 눌러 첫 번째 세트 할인을 추가해보세요',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey.shade500,
            ),
          ),
        ],
      ),
    );
  }

  /// 세트 할인 목록 위젯
  Widget _buildSetDiscountList(List<SetDiscount> setDiscounts) {
    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      itemCount: setDiscounts.length,
      itemBuilder: (context, index) {
        final setDiscount = setDiscounts[index];
        return _buildSetDiscountCard(setDiscount);
      },
    );
  }

  /// 세트 할인 카드 위젯
  Widget _buildSetDiscountCard(SetDiscount setDiscount) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: setDiscount.isActive 
              ? Theme.of(context).primaryColor 
              : Colors.grey.shade400,
          child: Icon(
            Icons.local_offer,
            color: Colors.white,
            size: 20,
          ),
        ),
        title: Text(
          setDiscount.name,
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: setDiscount.isActive ? null : Colors.grey.shade600,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '할인 금액: ${CurrencyUtils.formatCurrency(setDiscount.discountAmount)}',
              style: TextStyle(
                color: setDiscount.isActive ? Colors.green.shade700 : Colors.grey.shade500,
                fontWeight: FontWeight.w500,
              ),
            ),
            Text(
              '상품 ${setDiscount.productIds.length}개 포함',
              style: TextStyle(
                color: setDiscount.isActive ? null : Colors.grey.shade500,
              ),
            ),
            if (!setDiscount.isActive)
              Text(
                '비활성화됨',
                style: TextStyle(
                  color: Colors.red.shade600,
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                ),
              ),
          ],
        ),
        trailing: PopupMenuButton<String>(
          onSelected: (value) => _handleMenuAction(value, setDiscount),
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'edit',
              child: Row(
                children: [
                  Icon(Icons.edit, size: 18),
                  SizedBox(width: 8),
                  Text('수정'),
                ],
              ),
            ),
            PopupMenuItem(
              value: 'toggle',
              child: Row(
                children: [
                  Icon(
                    setDiscount.isActive ? Icons.visibility_off : Icons.visibility,
                    size: 18,
                  ),
                  const SizedBox(width: 8),
                  Text(setDiscount.isActive ? '비활성화' : '활성화'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'delete',
              child: Row(
                children: [
                  Icon(Icons.delete, size: 18, color: Colors.red),
                  SizedBox(width: 8),
                  Text('삭제', style: TextStyle(color: Colors.red)),
                ],
              ),
            ),
          ],
        ),
        onTap: () => _showEditDialog(setDiscount),
      ),
    );
  }

  /// 메뉴 액션 처리
  void _handleMenuAction(String action, SetDiscount setDiscount) {
    switch (action) {
      case 'edit':
        _showEditDialog(setDiscount);
        break;
      case 'toggle':
        _toggleSetDiscountActive(setDiscount);
        break;
      case 'delete':
        _showDeleteConfirmation(setDiscount);
        break;
    }
  }

  /// 세트 할인 추가 다이얼로그 표시
  void _showAddDialog() {
    showDialog(
      context: context,
      builder: (context) => const SetDiscountFormDialog(),
    );
  }

  /// 세트 할인 수정 다이얼로그 표시
  void _showEditDialog(SetDiscount setDiscount) {
    showDialog(
      context: context,
      builder: (context) => SetDiscountFormDialog(setDiscount: setDiscount),
    );
  }

  /// 세트 할인 활성화/비활성화
  void _toggleSetDiscountActive(SetDiscount setDiscount) async {
    final success = await ref
        .read(setDiscountNotifierProvider.notifier)
        .toggleSetDiscountActive(setDiscount.id!, !setDiscount.isActive);

    if (success && mounted) {
      ToastUtils.showSuccess(
        context,
        setDiscount.isActive
            ? '${setDiscount.name}이(가) 비활성화되었습니다'
            : '${setDiscount.name}이(가) 활성화되었습니다',
      );
    }
  }

  /// 삭제 확인 다이얼로그 표시
  void _showDeleteConfirmation(SetDiscount setDiscount) async {
    final confirmed = await ConfirmationDialog.show(
      context: context,
      title: '세트 할인 삭제',
      message: '${setDiscount.name}을(를) 삭제하시겠습니까?\n이 작업은 되돌릴 수 없습니다.',
      confirmLabel: '삭제',
      cancelLabel: '취소',
    );

    if (confirmed == true) {
      final success = await ref
          .read(setDiscountNotifierProvider.notifier)
          .deleteSetDiscount(setDiscount.id!);

      if (success && mounted) {
        ToastUtils.showSuccess(
          context,
          '${setDiscount.name}이(가) 삭제되었습니다',
        );
      }
    }
  }
}
