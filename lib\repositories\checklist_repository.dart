/// Blue Booth Manager - 체크리스트 데이터 Repository
///
/// 체크리스트 템플릿과 아이템 데이터의 CRUD 기능을 제공하는 Repository 클래스입니다.
/// - 로컬 DB(SQLite)와 연동하며, 실시간 동기화 구조를 지원합니다.
/// - 모든 메서드는 SQL injection 방지, 보안/안정성 고려.
///
/// 주요 기능:
/// - 체크리스트 템플릿 CRUD (생성, 조회, 수정, 삭제)
/// - 체크리스트 아이템 CRUD (행사별 체크 상태 관리)
/// - 정렬 및 필터링 (순서, 활성 상태)
/// - 검색 기능 (제목별)
/// - 실시간 동기화 지원
///
/// 보안 기능:
/// - SQL injection 방지
/// - 입력값 검증 및 이스케이프 처리
///
/// 작성자: Blue
/// 버전: 1.0.0
/// 최종 업데이트: 2025년 8월
library;

import 'package:sqflite/sqflite.dart';
import '../models/checklist_template.dart';
import '../models/checklist_item.dart';
import '../services/database_service.dart';
import '../utils/logger_utils.dart';

class ChecklistRepository {
  static const String _tag = 'ChecklistRepository';
  
  final DatabaseService _databaseService;
  
  ChecklistRepository(this._databaseService);
  
  Future<Database> get _database => _databaseService.database;

  // ============================================================================
  // 체크리스트 템플릿 관련 메서드
  // ============================================================================

  /// 모든 체크리스트 템플릿을 순서대로 조회합니다.
  Future<List<ChecklistTemplate>> getAllTemplates() async {
    final stopwatch = Stopwatch()..start();
    
    final db = await _database;
    
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseServiceImpl.checklistTemplatesTable,
      where: 'isActive = ?',
      whereArgs: [1],
      orderBy: '`order` ASC, createdAt ASC',
    );

    final templates = maps.map((map) => ChecklistTemplate.fromMap(map)).toList();

    stopwatch.stop();
    LoggerUtils.logInfo('체크리스트 템플릿 조회 완료: ${templates.length}개, 소요시간: ${stopwatch.elapsedMilliseconds}ms', tag: _tag);

    return templates;
  }

  /// 체크리스트 템플릿을 신규 등록합니다.
  Future<int> insertTemplate(ChecklistTemplate template) async {
    final db = await _database;

    LoggerUtils.logInfo('체크리스트 템플릿 DB 삽입 시작 - 제목: ${template.title}', tag: _tag);

    // 1. 제목 중복 체크
    final List<Map<String, dynamic>> existingTemplates = await db.query(
      DatabaseServiceImpl.checklistTemplatesTable,
      where: 'title = ? AND isActive = ?',
      whereArgs: [template.title, 1],
    );

    if (existingTemplates.isNotEmpty) {
      LoggerUtils.logWarning('체크리스트 템플릿 제목 중복 - "${template.title}" 템플릿이 이미 존재합니다.', tag: _tag);
      throw Exception('같은 제목의 체크리스트가 이미 존재합니다.');
    }

    // 2. 로컬 DB에 저장
    final insertedId = await db.insert(
      DatabaseServiceImpl.checklistTemplatesTable,
      template.toMap(),
    );

    LoggerUtils.logInfo('체크리스트 템플릿 DB 삽입 성공 - ID: $insertedId, 제목: ${template.title}', tag: _tag);
    
    return insertedId;
  }

  /// 체크리스트 템플릿을 수정합니다.
  Future<void> updateTemplate(ChecklistTemplate template) async {
    final db = await _database;

    LoggerUtils.logInfo('체크리스트 템플릿 수정 시작 - ID: ${template.id}, 제목: ${template.title}', tag: _tag);

    final updatedTemplate = template.copyWith(
      updatedAt: DateTime.now(),
    );

    final rowsAffected = await db.update(
      DatabaseServiceImpl.checklistTemplatesTable,
      updatedTemplate.toMap(),
      where: 'id = ?',
      whereArgs: [template.id],
    );

    if (rowsAffected == 0) {
      throw Exception('체크리스트 템플릿을 찾을 수 없습니다.');
    }

    LoggerUtils.logInfo('체크리스트 템플릿 수정 완료 - ID: ${template.id}', tag: _tag);
  }

  /// 체크리스트 템플릿을 삭제합니다 (소프트 삭제).
  Future<void> deleteTemplate(int templateId) async {
    final db = await _database;

    LoggerUtils.logInfo('체크리스트 템플릿 삭제 시작 - ID: $templateId', tag: _tag);

    final rowsAffected = await db.update(
      DatabaseServiceImpl.checklistTemplatesTable,
      {
        'isActive': 0,
        'updatedAt': DateTime.now().millisecondsSinceEpoch,
      },
      where: 'id = ?',
      whereArgs: [templateId],
    );

    if (rowsAffected == 0) {
      throw Exception('체크리스트 템플릿을 찾을 수 없습니다.');
    }

    LoggerUtils.logInfo('체크리스트 템플릿 삭제 완료 - ID: $templateId', tag: _tag);
  }

  // ============================================================================
  // 체크리스트 아이템 관련 메서드
  // ============================================================================

  /// 특정 행사의 체크리스트 아이템들을 조회합니다.
  Future<List<ChecklistItem>> getItemsByEventId(int eventId) async {
    final stopwatch = Stopwatch()..start();
    
    final db = await _database;
    
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseServiceImpl.checklistItemsTable,
      where: 'eventId = ?',
      whereArgs: [eventId],
      orderBy: 'templateId ASC',
    );

    final items = maps.map((map) => ChecklistItem.fromMap(map)).toList();

    stopwatch.stop();
    LoggerUtils.logInfo('체크리스트 아이템 조회 완료: eventId=$eventId, ${items.length}개, 소요시간: ${stopwatch.elapsedMilliseconds}ms', tag: _tag);

    return items;
  }

  /// 체크리스트 아이템을 신규 등록하거나 업데이트합니다.
  Future<int> upsertItem(ChecklistItem item) async {
    final db = await _database;

    LoggerUtils.logInfo('체크리스트 아이템 upsert 시작 - templateId: ${item.templateId}, eventId: ${item.eventId}', tag: _tag);

    // 1. 기존 아이템 확인
    final List<Map<String, dynamic>> existingItems = await db.query(
      DatabaseServiceImpl.checklistItemsTable,
      where: 'templateId = ? AND eventId = ?',
      whereArgs: [item.templateId, item.eventId],
    );

    int result;
    if (existingItems.isNotEmpty) {
      // 업데이트
      final updatedItem = item.copyWith(
        id: existingItems.first['id'] as int,
        updatedAt: DateTime.now(),
      );
      
      await db.update(
        DatabaseServiceImpl.checklistItemsTable,
        updatedItem.toMap(),
        where: 'templateId = ? AND eventId = ?',
        whereArgs: [item.templateId, item.eventId],
      );
      
      result = updatedItem.id!;
      LoggerUtils.logInfo('체크리스트 아이템 업데이트 완료 - ID: $result', tag: _tag);
    } else {
      // 삽입
      result = await db.insert(
        DatabaseServiceImpl.checklistItemsTable,
        item.toMap(),
      );
      LoggerUtils.logInfo('체크리스트 아이템 삽입 완료 - ID: $result', tag: _tag);
    }
    
    return result;
  }

  /// 체크리스트 아이템의 체크 상태를 토글합니다.
  Future<void> toggleItemCheck(int templateId, int eventId) async {
    final db = await _database;

    LoggerUtils.logInfo('체크리스트 아이템 체크 토글 시작 - templateId: $templateId, eventId: $eventId', tag: _tag);

    // 1. 현재 상태 조회
    final List<Map<String, dynamic>> existingItems = await db.query(
      DatabaseServiceImpl.checklistItemsTable,
      where: 'templateId = ? AND eventId = ?',
      whereArgs: [templateId, eventId],
    );

    if (existingItems.isNotEmpty) {
      // 기존 아이템 업데이트
      final currentItem = ChecklistItem.fromMap(existingItems.first);
      final toggledItem = currentItem.toggleCheck();
      
      await db.update(
        DatabaseServiceImpl.checklistItemsTable,
        toggledItem.toMap(),
        where: 'templateId = ? AND eventId = ?',
        whereArgs: [templateId, eventId],
      );
    } else {
      // 새 아이템 생성 (체크됨 상태로)
      final newItem = ChecklistItem.create(
        templateId: templateId,
        eventId: eventId,
        isChecked: true,
      );
      
      await db.insert(
        DatabaseServiceImpl.checklistItemsTable,
        newItem.toMap(),
      );
    }

    LoggerUtils.logInfo('체크리스트 아이템 체크 토글 완료 - templateId: $templateId, eventId: $eventId', tag: _tag);
  }

  /// 특정 템플릿과 연관된 모든 아이템들을 삭제합니다.
  Future<void> deleteItemsByTemplateId(int templateId) async {
    final db = await _database;

    LoggerUtils.logInfo('체크리스트 아이템 삭제 시작 - templateId: $templateId', tag: _tag);

    await db.delete(
      DatabaseServiceImpl.checklistItemsTable,
      where: 'templateId = ?',
      whereArgs: [templateId],
    );

    LoggerUtils.logInfo('체크리스트 아이템 삭제 완료 - templateId: $templateId', tag: _tag);
  }
}
